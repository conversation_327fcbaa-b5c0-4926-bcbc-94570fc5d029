<meta charset="UTF-8" /><meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title></title>
<style type="text/css">/* * {
  border: red 1px solid;
} */

  body {
    font-family: "Poppins", Arial, sans-serif;
    background-color: #f7f9fc;
    margin: 0;
    padding: 0;
    color: #003764;
  }

  input[type='text'] {
    color: red;
    width: 50px;
    align-items: center;
  }

  .container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
  }

  .tabs {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    background-color: #003764;
    border-radius: 5px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: auto;
  }

  .tab-button {
    flex: 1;
    padding: 10px 15px;
    margin: 5px;
    color: white;
    background-color: #003764;
    border: none;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    border-radius: 5px;
    white-space: nowrap;
  }

  .tab-button:hover,
  .tab-button.active {
    background-color: #48a849;
    transform: scale(1.05);
    color: white;
  }

  .tab-content {
    width: 100%;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    animation: fadeEffect 0.5s;
    box-sizing: border-box;
    display: block;
    /* here is whre you change the visibilti */
  }

  .tab-content.active {
    display: block;
  }

  @keyframes fadeEffect {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  h2,
  h3,
  h4 {
    text-align: center;
    color: #003764;
    margin-bottom: 20px;
  }

  /* New div-based table styles */
  .standard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #003764;
    color: white;
    border-radius: 5px 5px 0 0;
    margin-bottom: 0;
    padding: 10px 15px;
    font-weight: bold;
    gap: 50px;
  }

  .standard-header-title {
    flex: 2;
    font-weight: bold;
    font-size: 16px;
  }

  .standard-header-score {
    width: 70px;
  }

  .standard-header-score input {
    width: 70px;
    text-align: center;
  }

  .div-table {
    width: 100%;
    margin: 20px 0;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .div-table-header {
    display: flex;
    background-color: #003764;
    color: white;
    font-weight: 600;
    font-size: 14px;
    padding: 10px;
    text-transform: uppercase;
  }

  .div-table-header-col1 {
    width: 25%;
  }

  .div-table-header-col2 {
    width: 45%;
  }

  .div-table-header-col3,
  .div-table-header-col4 {
    width: 15%;
    text-align: center;
  }

  .div-table-row {
    display: flex;
    border-bottom: 1px solid #eee;
  }

  .div-table-row:nth-child(even) {
    background: #f7f9fc;
  }

  .div-table-cell {
    padding: 10px;
  }

  .div-table-cell-col1 {
    width: 25%;
    background-color: white;
  }

  .div-table-cell-col2 {
    width: 45%;
  }

  .div-table-cell-col3 {
    width: 15%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .div-table-cell-col4 {
    width: 15%;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .level-row {
    border-bottom: 1px solid #eee;
  }

  .level-heading {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .level-description {
    margin-bottom: 10px;
  }

  .navigation-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
  }

  .navigation-buttons button {
    padding: 8px 16px;
    background-color: #003764;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .navigation-buttons button:hover {
    background-color: #48a849;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .div-table-row {
      flex-direction: column;
    }

    .div-table-cell-col1,
    .div-table-cell-col2,
    .div-table-cell-col3,
    .div-table-cell-col4 {
      width: 100%;
    }

    .div-table-header {
      display: none;
    }

    .tabs {
      justify-content: center;
    }

    .tab-button {
      font-size: 12px;
      flex: none;
      padding: 8px;
    }

    .tab-content {
      padding: 15px;
    }
  }
</style>
<div class="container"><!-- Tabs Navigation -->
<div class="tabs"><button class="tab-button active" onclick="openTab('section1')">RISK AREA 3</button><button class="tab-button" onclick="openTab('section2')">Standard 1</button><button class="tab-button" onclick="openTab('section3')">Standard 2</button><button class="tab-button" onclick="openTab('section4')">Standard 3</button><button class="tab-button" onclick="openTab('section5')">Standard 4</button><button class="tab-button" onclick="openTab('section6')">Standard 5</button><button class="tab-button" onclick="openTab('section7')">Standard 6</button><button class="tab-button" onclick="openTab('section8')">Standard 7</button><button class="tab-button" onclick="openTab('section9')">Standard 8</button><button class="tab-button" onclick="openTab('section10')">Standard 9</button><button class="tab-button" onclick="openTab('section11')">Standard 10</button><button class="tab-button" onclick="openTab('section12')">Standard 11</button><button class="tab-button" onclick="openTab('section13')">Standard 12</button><button class="tab-button" onclick="openTab('section14')">Standard 13</button><button class="tab-button" onclick="openTab('section15')">Standard 14</button><button class="tab-button" onclick="openTab('section16')">Standard 15</button><button class="tab-button" onclick="openTab('section17')">Standard 16</button><button class="tab-button" onclick="openTab('section18')">Standard 17</button></div>
<!-- Section 1: RISK AREA 1 -->

<div class="tab-content active" id="section1">
<h2><strong>RISK AREA #3 &ndash; SAFE AND FUNCTIONAL ENVIRONMENT FOR STAFF AND PATIENTS</strong></h2>

<p>Health care organizations are very complex places which house a significant amount of equipment, hazardous materials, and many types of patient supplies. Health care practitioners may be proficient in using equipment but may often lack the expertise to inspect and maintain the equipment. Those inspecting and maintaining equipment may not have the required skills and knowledge to ensure that equipment is functional and safe. Health care facilities typically undergo frequent remodeling or expansion, resulting in varying types and levels of fire safety conditions. These are a few examples of why heath care organizations are high-risk places for patients, staff, and visitors. Reducing environmental risks requires leadership commitment to safety, staff training, and regular inspection, maintenance, and monitoring.<br />
Patients and visitors usually do not understand the risks in the health care environment and assume conditions are safe. Because they are not prepared to be cautious on their own behalf, the organization must take appropriate actions to ensure that patients and staff are safe and to provide a protective and supportive environment.<br />
To provide safe patient care, service-specific resources are required. The physical facilities must be clean, functional, well ventilated, and well-lit. Sluice rooms, treatment rooms, dressing rooms and storage space (for example, for clean linen, cleaning equipment, medications, equipment, and supplies) should be available and secure to prevent unauthorized access. Sanitary and bathing facilities must accommodate the number of patients in the service and patient accommodation must provide for privacy and safety. The design of facility should provide for a logical flow of patient care, services, and tasks (for example, in theatre, CSSD, laundry and in specialized services such operating theatre, ICU and maternity).</p>
<!-- Required Documents Section -->

<div style="margin-left: 10px; margin-top: 50px">
<h3><strong>Required Documents</strong></h3>

<p style="margin-left: 160px">1. Planned preventative maintenance plan<br />
2. List of environmental risks<br />
3. Facility inspection report (See RA#1, St #11)<br />
4. Facility improvement plan<br />
5. Facility management plans:<br />
a. Fire safety<br />
b. Water management<br />
c. Power management<br />
6. Hazardous materials inventory, policies, and procedures<br />
7. Material Safety Data Sheets (MSDS)<br />
8. Environmental safety plans, policies, and procedures</p>

<p style="margin-left: 160px">9. Biomedical equipment inventory, policies, procedures, and replacement plans<br />
10. Biomedical and non-medical equipment maintenance records<br />
11. Reports of fire drills<br />
12. Reports of staff attendance for required training<br />
13. Reports for monthly safety rounds<br />
14. Site plan for electrical service distribution<br />
15. Minutes of Facility Safety Committee<br />
16. Infection prevention and control focal person job description<br />
17. Infection prevention and control policies and procedures and plan<br />
a. Hand hygiene<br />
b. Sterilization<br />
c. Laundry and linen services policies and procedures<br />
d. Proper storage and disposal of medical waste<br />
e. Communicable disease reporting<br />
18. Infection prevention and control surveillance data</p>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section2')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>

<div class="allStandards"><!-- Section 2: Standard 1 -->
<div class="tab-content" id="section2"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #1: Infrastructure, utilities, resources and equipment and furniture&nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-AuxeR7G2XXu-val" name="entryfield" title="RA3.S01.T" value="[ RA3.S01.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">The leaders plan the space, utilities, equipment, resources, and furniture needed to safely and effectively support the services provided and the plan is reviewed when the strategic and operational plans of the facility are reviewed.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no plan to ensure the provision of service-specific infrastructure, utilities, resources, equipment, and furniture.<br />
1 There is a plan to ensure the provision of service-specific infrastructure, utilities, resources, equipment, and furniture that is reviewed when the strategic and operational plans of the facility are reviewed and addresses at least the following:<br />
&bull; Patient accommodation and ablution facilities (including space to accommodate newborn caregivers)<br />
&bull; All mothers of small and sick newborns have a dedicated area for supportive adequate space for KMC, family centered care, privacy for mothers to express breast milk and facilities for hygiene.<br />
&bull; Administrative and clinical work areas<br />
&bull; Patient and workflow (for clinical and non-clinical areas)<br />
&bull; Infection prevention and control measures<br />
&bull; Risk prevention measures<br />
&bull; Essential furniture, equipment, medications, and supplies (checklists)<br />
&bull; Planned preventative maintenance for infrastructure, utilities, furniture, and equipment</p>

<p>2 There is evidence that each service was consulted and provided input into the plan.<br />
3 There is evidence that professional recommendations and national guidelines were used guide the provisions of the plan (for example, for maternal, newborn, child and adolescent health, mental health, operating theatre, laundry, CSSD, etc.).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-Dl6LjYSG9gp-val" name="entryfield" title="RA3.S01.L1" value="[ RA3.S01.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-xVxJGw8wjZC-val" name="entryfield" title="RA3.S01.L1.Sc" value="[ RA3.S01.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The space, utilities, equipment, resources, and furniture needed to support the services safely and effectively are provided and well maintained.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The space, equipment, utilities, resources, and furniture needed to support the services safely and effectively are not provided according to the plan or they are not well maintained.<br />
1 Documented evidence that the space, utilities, equipment, resources, and furniture are provided according to the plan (for example, routine inspections, checklists, etc.)<br />
2 Evidence that the infrastructure, utilities, equipment, and furniture in each service are reflected on the planned preventative maintenance schedule.<br />
3 Documented evidence that the space, equipment, resources, and furniture are maintained according to the plan (maintenance records should be referenced, for example, requests for maintenance, completion of requests and planned preventative maintenance records).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-NtEVr92M7fG-val" name="entryfield" title="RA3.S01.L2" value="[ RA3.S01.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-bSIXnRl67LL-val" name="entryfield" title="RA3.S01.L2.Sc" value="[ RA3.S01.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<p class="level-description" style="text-align: center;"><strong>Level 3:</strong></p>

<p class="level-description">Implementation of the plan to provide functional space, utilities, equipment, resources, and furniture is monitored and improvements made when required.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Implementation of the plan is not being monitored.<br />
1 Evidence that the plan is being monitored is available in at least the following documents:<br />
a. Inventory lists<br />
b. Environmental checklists<br />
c. Planned preventative maintenance records<br />
d. Completion of maintenance requests<br />
2 Data is aggregated and analyzed to identify trends and the information shared with a designated leader.<br />
3 Action plans are developed and implemented.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-coFTen6b2o6-val" name="entryfield" title="RA3.S01.L3" value="[ RA3.S01.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-kTTBSpwfpH9-val" name="entryfield" title="RA3.S01.L3.Sc" value="[ RA3.S01.L3.Sc ]" /></div>
</div>
<!-- Additional row for assessor notes -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section3')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>
<!-- Section 3: Standard 2 -->

<div class="tab-content" id="section3"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #2: Regular inspection of environmental safety&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-vAfzxZIkzl6-val" name="entryfield" title="RA3.S02.T" value="[ RA3.S02.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is an inspection process to identify and list health care environment risks of all types..</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no list of environmental risks, or it does not include all areas or types of risks.<br />
1 There is a list of environmental risks which includes input from all areas and includes issues identified in facility inspection reports from external agencies.<br />
2 A safety team (designated by the Health and Safety Committee) uses a checklist to identify risks when visiting areas on a monthly basis.<br />
3 There is a comprehensive list of all types of environmental risks in all areas that includes at least those relating to:<br />
a. Safety<br />
b. Security<br />
c. Hazardous materials<br />
d. Fire safety<br />
e. Biomedical equipment<br />
f. Utilities (power and water)<br />
g. Infection control (for example, laundry, sterilization, and waste management)<br />
NOTE: If during survey, the survey team identifies risks not included in the hospital&rsquo;s list, then a score of 3 cannot be awarded.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-q4E5HikeF5B-val" name="entryfield" title="RA3.S02.L1" value="[ RA3.S02.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-OjJVcBx2UgA-val" name="entryfield" title="RA3.S02.L1.Sc" value="[ RA3.S02.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The risks identified during the inspection process are prioritized according to severity and likelihood of occurrence and a plan is developed to reduce priority risks.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The environmental risks have not been prioritized.<br />
1 The environmental risks have been prioritized.</p>

<p>2 An interview with the biomedical maintenance officer reveals that all risks have been prioritized by the hospital&rsquo;s leadership.<br />
3 The risks have been prioritized by the hospital&rsquo;s leadership using a set of criteria.<br />
NOTE: The risk criteria would include at least:<br />
a. potential severity of an event, injury, or failure and<br />
b. likelihood of the event, injury, or failure occurring.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-c0IbJXz5oIP-val" name="entryfield" title="RA3.S02.L2" value="[ RA3.S02.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-JPSE2oPwmmm-val" name="entryfield" title="RA3.S02.L2.Sc" value="[ RA3.S02.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The risks identified are systematically reduced or eliminated, and the list is updated through periodic, routine re-inspections.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no plan for reducing the risks or it does not include specific responsible parties and target dates for completion.<br />
1 The facility improvement plans include actions to reduce the priority risks.<br />
2 Minutes or reports indicate that the facility improvement plan has been implemented.<br />
3 The implementation of the facility improvement plan is monitored at least quarterly as evidenced in meeting minutes.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-qF0cq8zYIlx-val" name="entryfield" title="RA3.S02.L3" value="[ RA3.S02.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-M013UvG94fm-val" name="entryfield" title="RA3.S02.L3.Sc" value="[ RA3.S02.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section2')">Previous</button><button onclick="openTab('section4')">Next</button></div>
</div>
<!-- Section 4: Standard 3 -->

<div class="tab-content" id="section4"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #3: Management of hazardous materials&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score">&nbsp;</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-mqvibJmaD6I-val" name="entryfield" title="RA3.S03.T" value="[ RA3.S03.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is an inventory of the all the locations, types, and volume of hazardous materials and a plan for management.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no list of hazardous materials.<br />
1 There is a list of hazardous materials that is available at departmental level.<br />
2 There is a comprehensive list of all types of hazardous materials in all areas.<br />
3 The list is updated on an annual basis.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-wE2tAt9RMUT-val" name="entryfield" title="RA3.S03.L1" value="[ RA3.S03.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Cyuz2eKO7x0-val" name="entryfield" title="RA3.S03.L1.Sc" value="[ RA3.S03.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Based on the plan, hazardous materials are safely and properly handled, labeled, stored, used, and disposed.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no policies and procedures for safe and proper handling, labeling, storage, use and disposal of hazardous materials.<br />
1 There are policies and procedures for safe and proper handling, labeling, storage and use of hazardous materials, including material safety data sheets (MSDS) available for staff reference for each of the hazardous materials.<br />
2 Staff is observed to be using appropriate PPE when handling hazardous materials.<br />
3 Monthly safety rounds are documented to ensure that hazardous materials are labeled, stored, and used properly, and that staff are using the appropriate PPE when handling hazardous materials.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-bRtPyP4ESSl-val" name="entryfield" title="RA3.S03.L2" value="[ RA3.S03.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-HZ41Wx4sARe-val" name="entryfield" title="RA3.S03.L2.Sc" value="[ RA3.S03.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Policies and procedures describe the required equipment, PPE, staff training,management and investigation of spills or accidents with hazardous materials.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no policies/procedures for managing spills or accidents with hazardous material.</p>

<p>1 Policies and procedures are in place on managing spills or accidents with hazardous materials that includes at least:<br />
a. Equipment and personal protective equipment required for each category of spill or accident<br />
b. Placement and accessibility to the equipment and personal protective equipment<br />
c. The method of managing each category of spill or accident<br />
d. Staff identified for training and competency assessment for each category of spill or accident (for example, clinical staff may need to be trained on chemotherapy and body fluid spills while technical staff may need to be trained on diesel spills)<br />
2 Staff have been trained and found competent on how to manage spills or accidents in their work environment (refer to the hospital&rsquo;s training plan) and the equipment and personal protective equipment required is readily available in the service areas.<br />
3 Spills and accidents involving hazardous materials are investigated and measures taken to prevent future spills and accidents and/or improve the response to such spills and accidents.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-pNOYri1mQPn-val" name="entryfield" title="RA3.S03.L3" value="[ RA3.S03.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-HGwSjrpD1k7-val" name="entryfield" title="RA3.S03.L3.Sc" value="[ RA3.S03.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section3')">Previous</button><button onclick="openTab('section5')">Next</button></div>
</div>
<!-- Section 5: Standard 4 -->

<div class="tab-content" id="section5"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #4: Fire safety and disaster management&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-o5SBjPxHnSs-val" name="entryfield" title="RA3.S04.T" value="[ RA3.S04.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is a program for fire safety and disaster management that is specific to the hospital and includes training, prevention, early detection, and safe exit of staff and patients.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no fire safety and disaster management plan.<br />
1 The fire safety plan and disaster management plan are present but not specific to this hospital.<br />
2 The fire safety and disaster management plan has been developed that is specific to the hospital and the specific requirement of each department and includes:<br />
a. Training<br />
b. Allocation of staff roles and responsibilities in each department in the event of a fire or disaster<br />
c. The identification of department-specific risks<br />
d. Prevention<br />
e. Early detection (for example, by smoke detectors or regular patrols)</p>

<p>f. Communication (for example, by electronic or manual alarm or use of whistles)<br />
g. Abatement (for example, by extinguishers or functional fire hose)<br />
h. Safe exit of staff and patients<br />
3 Documentation shows that the fire safety plan was developed in collaboration with the fire brigade/police and that the disaster management plan was developed in collaboration with local authorities.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-BvLlhx2EFd4-val" name="entryfield" title="RA3.S04.L1" value="[ RA3.S04.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-gETqBJJTsvA-val" name="entryfield" title="RA3.S04.L1.Sc" value="[ RA3.S04.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The fire safety and disaster management program has been implemented throughout the organization and sufficient equipment is available and functioning.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Annual staff training on the fire safety and disaster management plan is not reflected on the hospital&rsquo;s training plan or staff have not been trained according to the training plan.<br />
1 Annual staff training on the fire safety and disaster management plan is reflected on the hospital&rsquo;s training plan and staff have been trained within the past 12 months on both the hospital-wide plan and department-specific plans.<br />
2 The staff are able to describe their allocated roles and responsibilities in responding to a fire or disaster (including for example, bomb threats, hostage taking, etc.) and how to evacuate patients.<br />
3 Sufficient fire equipment (extinguishers, water hoses and water supply, and exit signage) is available and functioning through routine maintenance.<br />
<strong>NOTE: </strong>The training should not be limited to the use of fire extinguishers staff need to be trained in all aspects of the fire safety and disaster management plan. Staff need to practice their specific roles/responsibilities and communication skills as well as department specific requirements (for example, what is the plan if a fire breaks out in theatre and a patient&rsquo;s abdomen is open? How are ventilated patients, children and newborns being evacuated?)</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-fG58c75QyG8-val" name="entryfield" title="RA3.S04.L2" value="[ RA3.S04.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-vz3dtad8Dm1-val" name="entryfield" title="RA3.S04.L2.Sc" value="[ RA3.S04.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The fire safety and disaster management program is continually monitored and tested annually, and the results are used to continually improve fire safety.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no monthly fire safety rounds.</p>

<p>1 Fire safety rounds are conducted monthly, and actions taken to correct issues, for example, locked fire exits or blocked fire extinguishers.<br />
2 Annual rehearsals of the fire safety and disaster management plan have been carried out and include:<br />
a. A hospital-wide fire drill that has been conducted and evaluated in collaboration with the fire brigade<br />
b. A rehearsal of the disaster plan that has been conducted and evaluated in collaboration with local authorities<br />
c. A department-specific rehearsal in each department of the hospital (this can be conducted by the department managers)<br />
3 A plan for improvement is developed and implemented based on findings of safety rounds and the evaluations of the fire and disaster management drills.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-LkPR9FFkEhz-val" name="entryfield" title="RA3.S04.L3" value="[ RA3.S04.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-LkXxUxYxdwv-val" name="entryfield" title="RA3.S04.L3.Sc" value="[ RA3.S04.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 2 --><!-- Assessor Notes for Standard 2 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S02" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S02" name="notes_RA1_S02" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section4')">Previous</button><button onclick="openTab('section6')">Next</button></div>
</div>
<!-- Section 6: Standard 5 -->

<div class="tab-content" id="section6"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title"><span style="font-size: 14px">STANDARD #5 Biomedical equipment safety&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span>Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-X3rDYjQAlgj-val" name="entryfield" title="RA3.S05.T" value="[ RA3.S05.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is an inventory of all biomedical equipment and a replacement plan and comprehensive program for inspecting, testing, and maintaining biomedical equipment by qualified individuals is carried out.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no policies and procedures and/or replacement plan for biomedical equipment.<br />
1 Policies and procedures and a replacement plan for biomedical equipment are in place.<br />
2 Each piece of biomedical equipment has an inventory number attached to it and a complete inventory of biomedical equipment is present.<br />
3 All staff that maintain the equipment are trained.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-quLiQVUKfTV-val" name="entryfield" title="RA3.S05.L1" value="[ RA3.S05.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-iBKMU9S7Oqd-val" name="entryfield" title="RA3.S05.L1.Sc" value="[ RA3.S05.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">All biomedical equipment is appropriately inspected, tested, and maintained. Only trained and competent people handle specialized equipment.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Biomedical equipment has not been inspected and tested.<br />
1 All equipment is tagged with date of inspection/maintenance and next due date.<br />
2 Equipment management records show that all biomedical equipment is inspected, tested, and maintained on a scheduled basis by trained individuals.<br />
3 All staff that handles specialized equipment is trained and this is documented in their personnel file.<br />
NOTE: Identify staff members who handle specialized equipment, for example, ventilators, anesthesia machines, sterilizers and review their personnel files to determine that they have been trained.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-RJlbs7Qkkyu-val" name="entryfield" title="RA3.S05.L2" value="[ RA3.S05.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-EUFyCubzzha-val" name="entryfield" title="RA3.S05.L2.Sc" value="[ RA3.S05.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Data related to the program are used to reduce equipment breakdown and reduce risk to patients and staff.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The biomedical maintenance report does not reflect the biomedical equipment maintenance plan.</p>

<p>1 The biomedical maintenance report reflects the facility maintenance plan (may be evidenced through the biomedical maintenance software).<br />
2 The types of equipment and breakdowns are analyzed and reported to the safety committee at least quarterly.<br />
3 Actions are taken to reduce risks identified through data analysis.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-ZwclwhOiwjE-val" name="entryfield" title="RA3.S05.L3" value="[ RA3.S05.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Ik6n6NK2X1Q-val" name="entryfield" title="RA3.S05.L3.Sc" value="[ RA3.S05.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 3 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S03" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S03" name="notes_RA1_S03" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section5')">Previous</button><button onclick="openTab('section7')">Next</button></div>
</div>
<!-- Section 7: Standard 6 -->

<div class="tab-content" id="section7"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #6: Stable safe water sources&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-vG9vDOYeDBj-val" name="entryfield" title="RA3.S06.T" value="[ RA3.S06.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A plan describes the processes for maintaining a safe water supply.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no plan and budget line for water management.<br />
1 There is a plan for water management that includes:<br />
a. Current water sources and suppliers<br />
b. Storage of water<br />
c. Sources for obtaining emergency water supplies<br />
d. Consideration of previous water shortages<br />
e. The availability of supplies for water testing<br />
f. A budget for water management<br />
g. How many days the hospital can rely on stored water should the main water supply be interrupted<br />
2 The plan outlines methods to conserve water use.<br />
3 Agreements are in place for obtaining emergency water supplies for drinking, cooking, cleaning, sterilization, and handwashing in all clinical areas.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-gprsCdjfRf3-val" name="entryfield" title="RA3.S06.L1" value="[ RA3.S06.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Dg41ITXNBfI-val" name="entryfield" title="RA3.S06.L1.Sc" value="[ RA3.S06.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">A stable source of safe water and alternate sources are available; uninterrupted sources of clean water are available to support essential processes for patient care.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The plan for providing safe water is not implemented.<br />
1 Safe water is available according to the plan.<br />
2 All water containers are observed to be clean (surveyors to check on the main water tanks, out/in).<br />
3 Supplies are routinely available to test water.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-HgpPMOLpvMV-val" name="entryfield" title="RA3.S06.L2" value="[ RA3.S06.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-xNOEVglo82M-val" name="entryfield" title="RA3.S06.L2.Sc" value="[ RA3.S06.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The hospital ensures that the water is treated regularly and tested; the results&nbsp;are used to ensure patients have an uninterrupted supply of safe water.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no or inconsistent testing and treatment of water sources.</p>

<p>1 Records show that the water sources are tested on a weekly basis by the Environmental Health Officer, including pH, biological and chemical testing.<br />
2 Records show that water treatment is performed every five weeks or as needed by the hospital Environmental Health Officer.<br />
3 An evaluation of the management of water shortages is conducted and the results of the evaluation are used to make improvements in maintaining a safe water supply.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-WDyTVzDWayD-val" name="entryfield" title="RA3.S06.L3" value="[ RA3.S06.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-RTzDpknYyxX-val" name="entryfield" title="RA3.S06.L3.Sc" value="[ RA3.S06.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 5 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S05" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S05" name="notes_RA1_S05" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section6')">Previous</button><button onclick="openTab('section8')">Next</button></div>
</div>
<!-- Section 8: Standard 7 -->

<div class="tab-content" id="section8"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #7: Stable electricity sources&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-FRcLG5NAslq-val" name="entryfield" title="RA3.S07.T" value="[ RA3.S07.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Essential equipment and processes requiring electricity that support patient care have been identified.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no plan for power management.<br />
1 There is a plan for power management that describes the processes for maintaining electrical power to meet emergency needs that is supported by an adequate budget.<br />
2 A site plan showing electrical service entrance, distribution system, service transformer, and emergency generator location is posted in the power plant.<br />
3 Critical areas and equipment requiring back up have been identified in the plan, including NICU, ventilators, newborn resuscitation table, operating theater, maternity and pediatric services.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-mHl3aJOYGe0-val" name="entryfield" title="RA3.S07.L1" value="[ RA3.S07.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-G4OARNiAM9y-val" name="entryfield" title="RA3.S07.L1.Sc" value="[ RA3.S07.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">A process is in place to ensure an uninterrupted source of electrical power to essential equipment and processes.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Alternate sources of electrical power are not available, not included in the planned preventive maintenance schedule or are not well maintained.<br />
1 Alternate sources of power are available (for example, solar, generator or grid), included in the planned preventive maintenance schedule and maintained by trained staff who are competent.<br />
2 Maintenance staff interviewed can describe how to carry out the power management plan.<br />
3 Maintenance practices for emergency power systems are carried out according to policy and procedure and documented.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-wqYsCldDciu-val" name="entryfield" title="RA3.S07.L2" value="[ RA3.S07.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-XUhS1b4TvN2-val" name="entryfield" title="RA3.S07.L2.Sc" value="[ RA3.S07.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The organization tests the utilities program and uses the information to ensure patients are safe if electrical power is interrupted.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no or inconsistent testing of backup electric power.</p>

<p>1 The backup generator(s) is tested and documented on a weekly basis, including that there is sufficient oil/gasoline to run them.<br />
2 Emergency equipment that uses back up batteries (for example, defibrillators) and UPS systems (for example, for neonatal incubators or other critical equipment) are routinely tested at least quarterly.<br />
3 Actions are taken and documented to ensure reliability of the sources.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-ue3RDPM8YH7-val" name="entryfield" title="RA3.S07.L3" value="[ RA3.S07.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-we89rtCvn0J-val" name="entryfield" title="RA3.S07.L3.Sc" value="[ RA3.S07.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 7 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S07" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S07" name="notes_RA1_S07" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section7')">Previous</button><button onclick="openTab('section9')">Next</button></div>
</div>
<!-- Section 9: Standard 8 -->

<div class="tab-content" id="section9"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #8: Protection from aggression, violence, abuse and loss or damage to property&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-lriDUVAsFMS-val" name="entryfield" title="RA3.S08.T" value="[ RA3.S08.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">As part of the risk management strategy, the hospital has developed policies and procedures to protect patients, staff and visitors from aggression, violence, abuse and loss or damage to property.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Policies and procedures to protect patients, staff, volunteers and visitors from aggression, violence, abuse and loss or damage to property have not been developed.<br />
1 Policies and procedures to protect patients, staff, volunteers and visitors from aggression, violence, abuse and loss or damage to property have been developed and include at least:<br />
&bull; The identification of vulnerable patient groups<br />
&bull; The use of restraints<br />
&bull; The identification of vulnerable areas in the hospital<br />
&bull; The identification and management of restricted areas<br />
&bull; The management of aggression, violence, and abuse (physical, sexual, verbal, psychological and financial)<br />
&bull; The management of loss or damage to property<br />
&bull; Internal security<br />
&bull; External security<br />
&bull; Measures to ensure that all staff, volunteers, contract workers and independent practitioners are identified</p>

<p>&bull; How to summon the assistance of hospital security and the local security/police/protection service in the case of an emergency<br />
2 The resources required for the protection of patients, staff, and visitors from aggression, violence, abuse and loss or damage to property is included in the hospital&rsquo;s financial planning and reflected in the budget.<br />
3 Training for all staff, volunteers, contract workers and independent practitioners on protection from aggression, violence, abuse and loss or damage to property is included in the hospital&rsquo;s training plan.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-eogBzDY33Ok-val" name="entryfield" title="RA3.S08.L1" value="[ RA3.S08.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-SdBXvWtiFZi-val" name="entryfield" title="RA3.S08.L1.Sc" value="[ RA3.S08.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Staff are trained and patients, staff, and visitors are protected from aggression, violence, abuse and loss or damage to property.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Evidence of staff training, and implementation of the policies and procedures is not available and observed.<br />
1 There is evidence that staff training on protecting patients, staff, and visitors from aggression, violence, abuse and loss or damage to property has been given as planned.<br />
2 Staff can describe how to summon the assistance of hospital security and the local security/police/protection service in the case of an emergency.<br />
3 Internal and external security measures are in place and access to restricted areas are controlled.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-aycE8TPDXhK-val" name="entryfield" title="RA3.S08.L2" value="[ RA3.S08.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-OzGmaMyyDak-val" name="entryfield" title="RA3.S08.L2.Sc" value="[ RA3.S08.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The processes for protecting patients, staff, and visitors from aggression, violence, abuse and loss or damage to property are evaluated for effectiveness.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 No data or incomplete data is collected.<br />
1 Data is collected that includes at least:<br />
&bull; Incident and near-miss data<br />
&bull; Patient and staff complaints<br />
&bull; Staff training<br />
&bull; Staff identification<br />
&bull; Monitoring data for access to restricted areas<br />
2 Data are aggregated, analyzed, and shared with a designated leader.<br />
3 The data are used to identify trends and develop improvement plans.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-rg1nilT2tlB-val" name="entryfield" title="RA3.S08.L3" value="[ RA3.S08.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-SnDjftpJsHl-val" name="entryfield" title="RA3.S08.L3.Sc" value="[ RA3.S08.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 8 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S08" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S08" name="notes_RA1_S08" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section8')">Previous</button><button onclick="openTab('section10')">Next</button></div>
</div>
<!-- Section 10: Standard 9 -->

<div class="tab-content" id="section10"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #9: Coordination of infection prevention and control program&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-cVLnHtt2af8-val" name="entryfield" title="RA3.S09.T" value="[ RA3.S09.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A position description exists for an infection prevention and control (IPC) focal person, which is included in the personnel file.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no IPC focal person or no current job description.<br />
1 The IPC focal person&rsquo;s job description has been reviewed/updated within the past 24 months.<br />
2 The job description of an IPC focal person includes roles and responsibilities regarding:<br />
a. Program management<br />
b. Infection prevention and control activities<br />
c. Quality improvement and risk management<br />
d. Infection control committee<br />
e. Patient and staff education<br />
3 The IPC focal person has sufficient time allotted to carry out the roles and responsibilities, for example, routine surveillance of infections, data management. (Note: this will be a judgment based on observations that the program is being carried out as planned).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-GhWbMmaeiBJ-val" name="entryfield" title="RA3.S09.L1" value="[ RA3.S09.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-lzWNPwpPWU0-val" name="entryfield" title="RA3.S09.L1.Sc" value="[ RA3.S09.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The focal person has received sufficient training in infection prevention and control to fulfill the job responsibilities.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The IPC focal person has not received training in infection prevention and control.<br />
1 The IPC focal person has attended a general training program (for example, at least a 2-day workshop conducted by qualified individuals) in infection prevention and control.<br />
2 The focal person attends annual training activities to maintain and build capacity in current infection prevention and control practices.</p>

<p>3 The focal person successfully completes a competency-based infection prevention and control certification course provided by qualified trainers.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-QJVBYh3ZlPm-val" name="entryfield" title="RA3.S09.L2" value="[ RA3.S09.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-gTlw58ZXixc-val" name="entryfield" title="RA3.S09.L2.Sc" value="[ RA3.S09.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">A qualified IPC focal person carries out surveillance, data gathering, aggregation and analysis of infection prevention and control data, quality improvement activities and staff training.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The IPC focal person does not carry out routine surveillance activities.<br />
1 Data shows that the IPC focal person conducts routine surveillance for infection risks according to current practice guidelines (for example, CDC).<br />
2 Meeting minutes reflect that the IPC focal person guides IPC data analysis, action planning and quality improvement activities.<br />
3 The IPC focal person guides the development and staff training of the IPC policies and procedures and quality improvement activities.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-SeXtJMVEoxV-val" name="entryfield" title="RA3.S09.L3" value="[ RA3.S09.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-zZ6d6iYDBHN-val" name="entryfield" title="RA3.S09.L3.Sc" value="[ RA3.S09.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 9 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S09" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S09" name="notes_RA1_S09" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section9')">Previous</button><button onclick="openTab('section11')">Next</button></div>
</div>
<!-- Section 11: Standard 10 -->

<div class="tab-content" id="section11"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #10: Reduction of health care associated infections through hand hygiene&nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-VH4LkuYwL2b-val" name="entryfield" title="RA3.S10.T" value="[ RA3.S10.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Hand hygiene is emphasized and guided by evidence-based guidelines.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no hand hygiene policies/procedures.<br />
1 There are hand hygiene policies/procedures based on current practices (for example, WHO).<br />
2 Departments have trained and communicated the policies/procedures to staff (for example, at orientation, in-service training, posters above sinks).<br />
3 Staff interviewed are aware of hand hygiene policies and procedures.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-gIUkm6f4uTY-val" name="entryfield" title="RA3.S10.L1" value="[ RA3.S10.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-VlIKJg3s5X2-val" name="entryfield" title="RA3.S10.L1.Sc" value="[ RA3.S10.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">A consistent and effective hand hygiene program is in place with adequate equipment and supplies.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Adequate handwashing/hygiene facilities and supplies (including water, soap, disposable towels, and/or alcohol hand gel) are not consistently available.<br />
1 Adequate handwashing/hygiene facilities and supplies for staff, patient and visitor use are observed to be located in the areas that have been identified in the hand hygiene policy and procedure as ideal placement areas (for example, at entrances to the hospital and departments, at patient&rsquo;s bedsides, in sluice rooms, ablution facilities, waste storage area, etc.).<br />
2 The in-charge describes that a systematic process for ensuring availability of adequate supplies is evident, (for example, use of a daily checklist).<br />
3 Staff are observed to be performing hand hygiene according to the policies/procedures.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-IfeAPmVbiPE-val" name="entryfield" title="RA3.S10.L2" value="[ RA3.S10.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-JyDDvW4Rwr9-val" name="entryfield" title="RA3.S10.L2.Sc" value="[ RA3.S10.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Infection prevention and control data and hand hygiene surveillance data are used to improve the program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 No data or incomplete data is collected regarding hand hygiene practices.<br />
1 A standardized hygiene observation tool and method is used to collect data.<br />
2 Data are collected in all clinical areas on a scheduled basis.<br />
3 The data are aggregated and used to identify gaps and develop and implement improvement plans.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-TIz6qWVq9EA-val" name="entryfield" title="RA3.S10.L3" value="[ RA3.S10.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-bGVCwtQrx5F-val" name="entryfield" title="RA3.S10.L3.Sc" value="[ RA3.S10.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 10 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S10" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S10" name="notes_RA1_S10" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section10')">Previous</button><button onclick="openTab('section12')">Next</button></div>
</div>
<!-- Section 12: Standard 11 -->

<div class="tab-content" id="section12"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #11: Effective sterilization processes&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-sKKkEpQaMWV-val" name="entryfield" title="RA3.S11.T" value="[ RA3.S11.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Decontamination and Sterilization processes are guided by evidence-based policies and procedures carried out by competent staff with adequate equipment and supplies.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The required decontamination and sterilization policies or procedures are not present and/or complete.<br />
1 Current evidence-based policies and procedures are written for:<br />
a. Cleaning, Decontamination, and disinfection processes for surgery, CSSD, and patient care units.<br />
b. Cleaning, Decontamination and disinfection processes for laundry, kitchen, and mortuary (housekeeping)<br />
c. Sterilization techniques (for example, sterilization times, temperatures, and humidity)<br />
d. Reuse of single use devices.<br />
e. General cleaning for critical areas (examples: Theater, neonatal units, isolation rooms, etc.)<br />
2 Each person who reprocesses instruments receives initial and annual competency testing.<br />
3 Equipment and supplies necessary to implement decontamination and sterilization policies and procedures are present and in good working order.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-PujaGPV1Nh1-val" name="entryfield" title="RA3.S11.L1" value="[ RA3.S11.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-uvHF41cTgcS-val" name="entryfield" title="RA3.S11.L1.Sc" value="[ RA3.S11.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Consistent and effective decontamination and&nbsp;sterilization process are in place.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Decontamination and sterilization processes are not functioning or are observed to create a potential for cross-contamination.<br />
1 Cross-contamination is observed to be prevented in the CSSD cleaning area, laundry, kitchen and by housekeeping staff.</p>

<p>2 Staff interviewed indicates that before use on each patient, non-critical (stethoscope, thermometers, Blood pressure cuffs), critical medical and surgical devices and instruments are Cleaned, decontaminated, sterilized, including dental instruments (for example, extraction forceps, scalpel handles).<br />
3 Staff interviewed indicates that at a minimum, high-level disinfection is provided for semi-critical patient care equipment (for example, gastrointestinal endoscopes, endotracheal tubes, anesthesia breathing circuits, and respiratory therapy equipment).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-BDwY3qBLIpw-val" name="entryfield" title="RA3.S11.L2" value="[ RA3.S11.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-XJghc3r8lW6-val" name="entryfield" title="RA3.S11.L2.Sc" value="[ RA3.S11.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">There is documented evidence that complete sterilization has been accomplished.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Processes (for example, indicators) to verify complete sterilization are not present or inconsistent.<br />
1 Policies and procedures are in place for each type of monitoring technique including:<br />
a. How to perform the testing<br />
b. How often testing should be done<br />
c. How the results are documented<br />
d. Timeframe for maintaining sterilization records<br />
2 Mechanical, biological, and chemical monitors are used to ensure the effectiveness of the sterilization process and results are documented.<br />
3 Documentation indicates that each load is monitored with mechanical (for example, time, temperature, pressure) and chemical (internal and external) indicators (If the internal chemical indicator is visible, an external indicator is not needed).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-CDvyuR6YtNb-val" name="entryfield" title="RA3.S11.L3" value="[ RA3.S11.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-SqvO3KuNUvf-val" name="entryfield" title="RA3.S11.L3.Sc" value="[ RA3.S11.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 11 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S11" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S11" name="notes_RA1_S11" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section11')">Previous</button><button onclick="openTab('section13')">Next</button></div>
</div>
<!-- Section 13: Standard 12 -->

<div class="tab-content" id="section13"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #12: Effective laundry and linen services&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-BspYwA9GOi3-val" name="entryfield" title="RA3.S12.T" value="[ RA3.S12.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Current evidence-based policies and procedures guide the operation and maintenance of laundry and linen services.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Laundry and linen policies and procedures are not present, incomplete, or out of date.<br />
1 Current policies and procedures are present based on evidence-based guidelines and include at least:<br />
a. Separation of personnel working in clean and soiled areas<br />
b. Transportation of used or soiled linen from service units<br />
c. Marking of linen to identify ownership<br />
d. Handling of damaged and/or stained linen<br />
e. Delivery of clean linen<br />
f. How to obtain clean linen in an emergency<br />
g. Handling of infected linen including high risk infections such as viral hemorrhagic fevers<br />
h. Handling of linen infested with parasites<br />
i. Wearing of protective clothing<br />
j. Searching of used linen for sharps<br />
k. Sorting of linen<br />
l. Labelling of high-risk linen within the facility (for example, biohazard, radioisotopes, chemotherapy, etc.)<br />
m. Time/temperature combinations of different types of soiled and infected linen<br />
n. Classification of work for processing, for example, colors, fabrics, degree of soiling</p>

<p>o. Maximum capacity loading of machines (weight of dry fabric to cubic capacity of the machine)<br />
p. Maximum capacity loading of dryers (weight of wet fabric to cubic capacity of the machine)<br />
q. Use of, for example, chemicals, soaps, sodium hypochlorite solutions and softeners (including the availability of MSDS for each chemical)<br />
r. Finishing processes and folding of clean linen<br />
2 Laundry equipment is included in the planned preventive maintenance plan.<br />
3 Staff members interviewed are aware of the policies and procedures.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-q8K2oAlHM73-val" name="entryfield" title="RA3.S12.L1" value="[ RA3.S12.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-rooyCDeG9GT-val" name="entryfield" title="RA3.S12.L1.Sc" value="[ RA3.S12.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Consistent and effective laundry and linen processes are in place with adequate equipment and supplies.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The washing machines are not functional, or the capacity is not sufficient to meet the demands.<br />
1 There is an adequate supply of functioning automated washing machines, and water.<br />
2 Adequate, acceptable cleaning supplies and detergents are available to meet workload demands.<br />
3 Operation of laundry and linen services is observed to be carried out according to the policies and procedures including good separation of clean and dirty processes.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-ZpFSRZ9Uwr9-val" name="entryfield" title="RA3.S12.L2" value="[ RA3.S12.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-WUVdVHP7y1k-val" name="entryfield" title="RA3.S12.L2.Sc" value="[ RA3.S12.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">There is a quality control program for laundry and linen services.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no quality control plan for laundry and linen services, or it is inconsistently carried out.<br />
1 The quality control plan includes:<br />
a. Process to monitoring implementation of policies and procedures<br />
b. Implementation of the planned preventive maintenance, cleaning, disinfection, and decontamination schedule based on the operation performed<br />
c. Analyzing reported incidents, near misses and patient complaints (for example, sharps found in the linen, shortage of linen, damaged linen etc.)<br />
2 Records indicate that the plan is being followed.<br />
3 Action plans are developed and implemented to address quality control issues.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-N9MgJfl359V-val" name="entryfield" title="RA3.S12.L3" value="[ RA3.S12.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-zmvnBSnCCqw-val" name="entryfield" title="RA3.S12.L3.Sc" value="[ RA3.S12.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 12 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S12" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S12" name="notes_RA1_S12" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section12')">Previous</button><button onclick="openTab('section14')">Next</button></div>
</div>
<!-- Section 14: Standard 13 -->

<div class="tab-content" id="section14"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD# 13: Reduction of health care associated infections&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-OIpEKlsJMkR-val" name="entryfield" title="RA3.S13.T" value="[ RA3.S13.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">An infection prevention and control plan with measurable quality goals is in place that guides the implementation of the program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no IPC program.<br />
1 There is an IPC program based on current practices including an IPC focal person, a functional committee, policies and procedures and a plan with measurable goals.<br />
2 There is annual training of all staff regarding IPC based on identified needs.<br />
3 Staff interviewed are aware of the IPC policies and procedures.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-ZivfDGjOxdf-val" name="entryfield" title="RA3.S13.L1" value="[ RA3.S13.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-NN0phEQ4oDy-val" name="entryfield" title="RA3.S13.L1.Sc" value="[ RA3.S13.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Risks of health care associated infections are identified for patients, staff, and visitors and measures taken to reduce the risks.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Risks of infection have not been identified for patients, staff, and visitors.<br />
1 Risks of infection have been identified for patients, staff, and visitors.<br />
2 Policies and procedures are developed to manage risks that are identified.<br />
3 The staff is observed to be carrying out the IPC policies and procedures.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-xZSl1wU5prX-val" name="entryfield" title="RA3.S13.L2" value="[ RA3.S13.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-bueAqbq151v-val" name="entryfield" title="RA3.S13.L2.Sc" value="[ RA3.S13.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The infection prevention and control program is evaluated for effectiveness in reducing the incidence of health care associated infections, through monitoring infection rates.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The infection prevention and control program has not been evaluated.<br />
1 The surveillance data are aggregated, analyzed, and displayed in relevant departments by type of infection, (for example, neonatal infections, urinary tract, blood stream, ventilator associated and post op infections surgical site infections).<br />
2 The data are used to develop and implement plans for reducing infection rates.<br />
3 The overall infection prevention and control program is evaluated every 12 months and improvements made based on findings.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-xuXFZwIc64u-val" name="entryfield" title="RA3.S13.L3" value="[ RA3.S13.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-l9MCkADOJcJ-val" name="entryfield" title="RA3.S13.L3.Sc" value="[ RA3.S13.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 13 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S13" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S13" name="notes_RA1_S13" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section13')">Previous</button><button onclick="openTab('section15')">Next</button></div>
</div>
<!-- Section 15: Standard 14 -->

<div class="tab-content" id="section15"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD # 14: Barrier techniques available and used&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-wHo83UeeuCD-val" name="entryfield" title="RA3.S14.T" value="[ RA3.S14.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">The situations in which personal protective equipment and isolation techniques are to be used have been identified; policies and procedures developed and made known to staff.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Policies and procedures regarding the use of personal protective equipment (PPEs) and isolation techniques have not been developed.<br />
1 Policies and procedures regarding use of PPEs and isolation techniques have been developed and are based on current practice and available in all non-clinical/clinical areas.<br />
2 The staff has been trained regarding the use of PPEs and isolation techniques.<br />
3 The staff interviewed are aware of the proper use of PPEs and isolation techniques.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-B2ffSP4cbb4-val" name="entryfield" title="RA3.S14.L1" value="[ RA3.S14.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-ZGHO984fCfu-val" name="entryfield" title="RA3.S14.L1.Sc" value="[ RA3.S14.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Barrier techniques are used for those identified situations, supplies are available and accessible, and the techniques are used correctly.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 PPEs are not available in all departments as required.<br />
1 PPE supplies and equipment are available and convenient to staff in all locations.<br />
2 Staff are observed using the PPEs according to recommended practice.<br />
3 Isolation rooms are available and equipped and used according to recommended practice.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-IO3Tm0c30C7-val" name="entryfield" title="RA3.S14.L2" value="[ RA3.S14.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-EluNkErXmnC-val" name="entryfield" title="RA3.S14.L2.Sc" value="[ RA3.S14.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">There are data on the use of personal protective equipment and isolation techniques that contributes to the continuous improvement in correct use.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Data are not collected regarding the use of PPEs and isolation techniques.<br />
1 Data are collected regarding the use of PPEs and isolation techniques.<br />
2 The data are aggregated, analyzed, and displayed.<br />
3 The data are used to develop and implement plans for improving use of PPEs and isolation techniques.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-NgrZ7DTXT81-val" name="entryfield" title="RA3.S14.L3" value="[ RA3.S14.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Bj0tbhH5TQy-val" name="entryfield" title="RA3.S14.L3.Sc" value="[ RA3.S14.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 14 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S14" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S14" name="notes_RA1_S14" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section14')">Previous</button><button onclick="openTab('section16')">Next</button></div>
</div>
<!-- Section 16: Standard 15 -->

<div class="tab-content" id="section16"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD # 15: Proper disposal of sharps and needles&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-V3EkoSMCdfe-val" name="entryfield" title="RA3.S15.T" value="[ RA3.S15.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure provides guidance on proper disposal of sharps and needles, which is made known to staff.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no policies and procedures regarding disposal of sharps and needles.<br />
1 Policies and procedures regarding disposal of sharps and needles are based on current practice.<br />
2 Staff interviewed are aware of the proper disposal of sharps and needles.<br />
3 Sufficient supplies of sharps containers are observed to be available in all relevant locations.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-aBDfGC1xrn2-val" name="entryfield" title="RA3.S15.L1" value="[ RA3.S15.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-J3e2mqjDPsj-val" name="entryfield" title="RA3.S15.L1.Sc" value="[ RA3.S15.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The disposal of sharps and needles is well organized and uniform, with disposable containers collected regularly and disposed of properly.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The disposal of sharps and needles is not well organized and uniform.<br />
1 Puncture-proof sharps containers are properly located and secured in all areas as described in the policies and procedures.<br />
2 Containers are observed to be no more than 3/4 full, sealed and disposed of according to policy/procedure.<br />
3 The sealed sharps containers are picked up on a routine schedule and stored in a separate, secured storage area.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-aG9YLQBwSlI-val" name="entryfield" title="RA3.S15.L2" value="[ RA3.S15.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-vTnlL94FfNM-val" name="entryfield" title="RA3.S15.L2.Sc" value="[ RA3.S15.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">There are data available on injuries and accidents related to sharps and needles; these data are then used to continually improve the program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 No data are collected related to needle sticks or sharps injuries.<br />
1 Data are collected related to needle stick and sharps injuries and near-misses (for example, needles found in the laundry, patient&rsquo;s beds, on the floor, etc.).<br />
2 Results of data are communicated to the Infection Prevention and Control Committee at least quarterly.<br />
3 Data are used to develop and implement plans to reduce the potential for injury.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-rzKVbU0g8hV-val" name="entryfield" title="RA3.S15.L3" value="[ RA3.S15.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Y2u7kJ1p1cv-val" name="entryfield" title="RA3.S15.L3.Sc" value="[ RA3.S15.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 15 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S15" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S15" name="notes_RA1_S15" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section15')">Previous</button><button onclick="openTab('section17')">Next</button></div>
</div>
<!-- Section 17: Standard 16 -->

<div class="tab-content" id="section17"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD # 16: Proper storage and disposal of infectious medical waste&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-RqaCMgArscB-val" name="entryfield" title="RA3.S16.T" value="[ RA3.S16.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Policies and procedures describe proper storage and disposal of medical waste.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There are no policies and procedures regarding storage and disposal of infectious medical waste.<br />
1 There are current evidence-based policies and procedures regarding storage and disposal of infectious medical waste by segregation and labeling of waste bins including noninfectious waste (food), anatomical waste (placenta) and wound dressings, and this has been budgeted on an annual budget plan.<br />
2 Staff is oriented on proper storage and disposal of infectious medical waste.<br />
3 Staff interviewed in all areas is able to describe proper storage and disposal of infectious medical waste.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-yaRq7QFfI0n-val" name="entryfield" title="RA3.S16.L1" value="[ RA3.S16.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-VR4p1dbVH07-val" name="entryfield" title="RA3.S16.L1.Sc" value="[ RA3.S16.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">A uniform storage and disposal process is used that includes all types of infectious waste collection, storage, and proper disposal. Equipment and supplies necessary to manage medical waste are routinely available.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The equipment and supplies for storing and disposing of infectious waste is inconsistently available.<br />
1 The equipment and supplies are available for storing and disposing of infectious waste, including PPEs.<br />
2 Infectious waste is observed to be segregated, bagged, and labeled according to policy and procedure.<br />
3 The storage and disposal sites, including incinerators and placenta pits, are observed to be well maintained and secure.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-XhMjI92M4YG-val" name="entryfield" title="RA3.S16.L2" value="[ RA3.S16.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-gEt8N4K3ckA-val" name="entryfield" title="RA3.S16.L2.Sc" value="[ RA3.S16.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The infectious medical waste storage and disposal process is part of the organization&rsquo;s infection prevention and control process and is regularly evaluated and improved when indicated.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The proper storage and disposal of medical waste is not evaluated on a regular basis.<br />
1 There is a scheduled (at least monthly) and implemented system for the inspection of waste storage and disposal.<br />
2 The results of the inspection are documented and reported to the Infection Prevention and Control Committee.<br />
3 Actions are taken to correct issues identified.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-dzY0yOCQygR-val" name="entryfield" title="RA3.S16.L3" value="[ RA3.S16.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-qfxwJ5apBCR-val" name="entryfield" title="RA3.S16.L3.Sc" value="[ RA3.S16.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 16 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S16" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S16" name="notes_RA1_S16" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section16')">Previous</button><button onclick="openTab('section18')">Next</button></div>
</div>
<!-- Section 18: Standard 17 -->

<div class="tab-content" id="section18"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD # 17: Monitoring, reporting, and preventing the spread of communicable diseases&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="iAQHGbwMfzi-eYs0vSbw0Qi-val" name="entryfield" title="RA3.S17.T" value="[ RA3.S17.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Policies, procedures, and protocols are in place for the monitoring, reporting, and preventing the spread of communicable diseases.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Current policies, procedures and protocols are not in place regarding monitoring, reporting, and preventing the spread of communicable diseases.<br />
1 Current policies, procedures and protocols are in place regarding monitoring, reporting, and preventing the spread of communicable diseases.<br />
2 Prevention programs are established based on identified community needs.<br />
3 A communicable disease outbreak management policy and procedure is in place which includes the management of epidemics and pandemics.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-zx2XZtUWdyk-val" name="entryfield" title="RA3.S17.L1" value="[ RA3.S17.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-BJ5KnR0Husy-val" name="entryfield" title="RA3.S17.L1.Sc" value="[ RA3.S17.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Monitoring, reporting, prevention and control policies, procedures and protocols are carried out.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Programs to monitor, report, prevent and control communicable diseases are not effectively managed.<br />
1 Effective promotional and education programs are provided to staff and community regarding prevention of communicable diseases.<br />
2 The childhood vaccination program and supply chain is implemented according to the guidelines and vaccination plan.<br />
3 Screening programs are in place to identify communicable diseases (for example, sexually transmitted infections and HIV).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-vtbp6GC3Wq2-val" name="entryfield" title="RA3.S17.L2" value="[ RA3.S17.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-Hp91vAIfJSo-val" name="entryfield" title="RA3.S17.L2.Sc" value="[ RA3.S17.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Communicable diseases are reported, and data are used to plan promotional and service delivery.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Communicable diseases are not reported according to MOH requirements.<br />
1 Communicable diseases are reported according to MOH requirements.<br />
2 The success of control and prevention efforts is evaluated on an annual basis.<br />
3 Communicable disease data is monitored, and the data is used to plan promotional and service delivery.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="iAQHGbwMfzi-fwRyDHgVYsz-val" name="entryfield" title="RA3.S17.L3" value="[ RA3.S17.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="iAQHGbwMfzi-cg90eklnGJU-val" name="entryfield" title="RA3.S17.L3.Sc" value="[ RA3.S17.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 17 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S17" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S17" name="notes_RA1_S17" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section17')">Previous</button><button onclick="openTab('section19')">Next</button></div>
</div>
</div>
</div>
<script>
  // Function to open a tab
  function openTab(tabId) {
    var i;
    var x = document.getElementsByClassName("tab-content");
    var tabButtons = document.getElementsByClassName("tab-button");

    // Hide all tab contents and remove active class from all buttons
    for (i = 0; i < x.length; i++) {
      x[i].style.display = "none";
      tabButtons[i].classList.remove("active");
    }

    // Display the selected tab content and add the active class to the corresponding button
    document.getElementById(tabId).style.display = "block";
    document.getElementById(tabId).classList.add("active");

    // Add active class to the tab button corresponding to the selected tab content
    for (i = 0; i < tabButtons.length; i++) {
      if (tabButtons[i].getAttribute("onclick").includes(tabId)) {
        tabButtons[i].classList.add("active");
      }
    }
  }

  // Function to sync the Score input value to the Overall Score input
  function syncOverall(scoreId, overallId) {
    var scoreElem = document.getElementById(scoreId);
    var overallElem = document.getElementById(overallId);
    if (scoreElem && overallElem) {
      overallElem.value = scoreElem.value;
    }
  }
</script>