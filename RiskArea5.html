<meta charset="UTF-8" /><meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title></title>
<style type="text/css">/* * {
  border: red 1px solid;
} */

    body {
        font-family: "Poppins", Arial, sans-serif;
        background-color: #f7f9fc;
        margin: 0;
        padding: 0;
        color: #003764;
    }

    input[type="text"] {
        color: red;
        width: 50px;
        align-items: center;
    }



    .container {
        max-width: 100%;
        margin: 0 auto;
        padding: 20px;
        box-sizing: border-box;
    }

    .tabs {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        background-color: #003764;
        border-radius: 5px;
        padding: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: auto;
    }

    .tab-button {
        flex: 1;
        padding: 10px 15px;
        margin: 5px;
        color: white;
        background-color: #003764;
        border: none;
        text-align: center;
        cursor: pointer;
        transition: background-color 0.3s ease, transform 0.2s ease;
        border-radius: 5px;
        white-space: nowrap;
    }

    .tab-button:hover,
    .tab-button.active {
        background-color: #48a849;
        transform: scale(1.05);
        color: white;
    }

    .tab-content {
        width: 100%;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        animation: fadeEffect 0.5s;
        box-sizing: border-box;
        display: block;
        /* here is whre you change the visibilti */
    }

    .tab-content.active {
        display: block;
    }

    @keyframes fadeEffect {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    h2,
    h3,
    h4 {
        text-align: center;
        color: #003764;
        margin-bottom: 20px;
    }

    /* New div-based table styles */
    .standard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #003764;
        color: white;
        border-radius: 5px 5px 0 0;
        margin-bottom: 0;
        padding: 10px 15px;
        font-weight: bold;
        gap: 50px;
    }

    .standard-header-title {
        flex: 2;
        font-weight: bold;
        font-size: 16px;
    }

    .standard-header-score {
        width: 70px;
    }

    .standard-header-score input {
        width: 70px;
        text-align: center;
    }

    .div-table {
        width: 100%;
        margin: 20px 0;
        background-color: #fff;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .div-table-header {
        display: flex;
        background-color: #003764;
        color: white;
        font-weight: 600;
        font-size: 14px;
        padding: 10px;
        text-transform: uppercase;
    }

    .div-table-header-col1 {
        width: 25%;
    }

    .div-table-header-col2 {
        width: 45%;
    }

    .div-table-header-col3,
    .div-table-header-col4 {
        width: 15%;
        text-align: center;
    }

    .div-table-row {
        display: flex;
        border-bottom: 1px solid #eee;
    }

    .div-table-row:nth-child(even) {
        background: #f7f9fc;
    }

    .div-table-cell {
        padding: 10px;
    }

    .div-table-cell-col1 {
        width: 25%;
        background-color: white;
    }

    .div-table-cell-col2 {
        width: 45%;
    }

    .div-table-cell-col3 {
        width: 15%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .div-table-cell-col4 {
        width: 15%;
        text-align: left;
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }

    .level-row {
        border-bottom: 1px solid #eee;
    }

    .level-heading {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .level-description {
        margin-bottom: 10px;
    }

    .navigation-buttons {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
    }

    .navigation-buttons button {
        padding: 8px 16px;
        background-color: #003764;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .navigation-buttons button:hover {
        background-color: #48a849;
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .div-table-row {
            flex-direction: column;
        }

        .div-table-cell-col1,
        .div-table-cell-col2,
        .div-table-cell-col3,
        .div-table-cell-col4 {
            width: 100%;
        }

        .div-table-header {
            display: none;
        }

        .tabs {
            justify-content: center;
        }

        .tab-button {
            font-size: 12px;
            flex: none;
            padding: 8px;
        }

        .tab-content {
            padding: 15px;
        }
    }
</style>
<div class="container"><!-- Tabs Navigation -->
<div class="tabs"><button class="tab-button active" onclick="openTab('section1')">RISK AREA 5</button><button class="tab-button" onclick="openTab('section2')">Standard 1</button><button class="tab-button" onclick="openTab('section3')">Standard 2</button><button class="tab-button" onclick="openTab('section4')">Standard 3</button><button class="tab-button" onclick="openTab('section5')">Standard 4</button><button class="tab-button" onclick="openTab('section6')">Standard 5</button><button class="tab-button" onclick="openTab('section7')">Standard 6</button><button class="tab-button" onclick="openTab('section8')">Standard 7</button><button class="tab-button" onclick="openTab('section9')">Standard 8</button><button class="tab-button" onclick="openTab('section10')">Standard 9</button></div>
<!-- Section 1: RISK AREA 1 -->

<div class="tab-content active" id="section1">
<h2><strong>RISK AREA #5 &ndash; IMPROVEMENT OF QUALITY AND SAFETY</strong></h2>

<p>Health care organizations, and their patients, remain at risk from poor quality and unsafe practices if organizations do not learn from their good and bad experiences and take actions to continually improve. Data are at the core of this learning. Organizations need to understand and value data collection and analysis in process improvement. Organizations must gain experience in setting improvement priorities, collecting data, displaying data for better analysis, and finally, planning and implementing improvement strategies. When leaders are committed to quality improvement and value the data that form the basis of evidence-based learning, the organization&rsquo;s culture is focused on quality and safety. This helps create a non-punitive environment and encourages an incident-reporting system. It embraces teamwork on all levels and includes patients as important members of their treatment teams and quality efforts.</p>
<!-- Required Documents Section -->

<div style="margin-left: 10px; margin-top: 50px">
<h3><strong>Required Documents</strong></h3>

<p style="margin-left: 160px">1. Quality and safety plan<br />
2. Customer care program<br />
3. Incident reporting policy and procedure<br />
4. Patient satisfaction policies, procedures, data, and actions for improvement.<br />
5. Staff satisfaction policies, procedures, data, and actions for improvement.<br />
6. Patient/family complaint policy and procedure, data, and actions for improvement<br />
7. Staff quality training plan and records<br />
8. Clinical outcomes monitoring data and actions for improvement</p>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section2')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>

<div class="allStandards"><!-- Section 2: Standard 1 -->
<div class="tab-content" id="section2"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #1: Quality and safety program&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-hPGPhEehtdF-val" name="entryfield" title="RA5.S01.T" value="[ RA5.S01.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">The roles and functions of the appointed Quality Improvement Officer are described in a job description and a quality and patient safety plan with terms of reference for the quality committee is written and guides the quality and patient safety program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no Quality Improvement Officer job description.<br />
1 A current Quality Improvement Officer job description is present.<br />
2 There is a hospital wide plan for quality improvement and patient safety, which includes at least the following:<br />
a. A definition of quality<br />
b. The description of quality improvement methods used in the hospital<br />
c. The membership of the quality improvement and patient safety committee and defines the leadership and responsibilities of the committee<br />
d. The coordination among all components of the organization&rsquo;s quality improvement and safety activities<br />
e. Hospital wide specific quality goals are identified<br />
f. Quality and safety indicators that are currently being measured<br />
3 Each quality indicator has a clear definition, formula, data collection method, who is responsible for data collection, frequency of data collection, and target.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-MAv2mssMghn-val" name="entryfield" title="RA5.S01.L1" value="[ RA5.S01.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-CygDKKmPGYa-val" name="entryfield" title="RA5.S01.L1.Sc" value="[ RA5.S01.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">A quality focal person is coordinating the quality and patient safety activities. The quality plan has been implemented and progress toward meeting goals/objectives is tracked through the quality and patient safety committee.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no Quality Improvement Officer has not attended a formal QI course.<br />
1 The Quality Improvement Officer has attended a formal course in QI and patient safety approaches.<br />
2 An interview with the QI committee indicates that the team is functioning.<br />
3 Meeting minutes show that the goals/objectives of the quality plan are being tracked on a quarterly basis and indicators are reported and acted upon according to the plan.</p>

<p><strong>NOTE:</strong> A formal course would consist of at least a 3-day workshop conducted by a qualified instructor.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-vvUUn24O9eO-val" name="entryfield" title="RA5.S01.L2" value="[ RA5.S01.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-LmxoSaFBNXM-val" name="entryfield" title="RA5.S01.L2.Sc" value="[ RA5.S01.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<p class="level-description" style="text-align: center;"><strong>Level 3:</strong></p>

<p class="level-description">The quality and patient safety plan is evaluated annually, and new goals/objectives and indicators set for the upcoming year.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The quality and patient safety plan is not evaluated annually.<br />
1 Minutes of the QI Committee indicate that the quality and safety plan has been evaluated within the last 15 months.<br />
2 Goals, objectives, and indicators for improving quality and safety have been established for the current year.<br />
3 The plan has been approved within the past 12 months.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-AIS7pHMTdMj-val" name="entryfield" title="RA5.S01.L3" value="[ RA5.S01.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-Hs7lIwtz5Y9-val" name="entryfield" title="RA5.S01.L3.Sc" value="[ RA5.S01.L3.Sc ]" /></div>
</div>
<!-- Additional row for assessor notes -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section3')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>
<!-- Section 3: Standard 2 -->

<div class="tab-content" id="section3"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #2: Effective customer care program&nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-cUtKymKr4eH-val" name="entryfield" title="RA5.S02.T" value="[ RA5.S02.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is an effective customer care program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no customer care program.<br />
1 A customer care program defines the workplace expectations and performance guidelines for customer service for all staff.<br />
2 A dress code is developed for all staff to present a professional image to the public that includes:<br />
a. Identification<br />
b. Clothing<br />
c. Shoes<br />
d. Nail care<br />
e. Jewelry<br />
f. Hair<br />
3 A job description is written for the Customer Care Officer and customer service expectations are included in all staff job descriptions.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-SJfJQ65JQH7-val" name="entryfield" title="RA5.S02.L1" value="[ RA5.S02.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-ic22MUFjqFb-val" name="entryfield" title="RA5.S02.L1.Sc" value="[ RA5.S02.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The patient and family are treated with respect and dignity and individual needs are met.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staff have not been trained in customer care.<br />
1 Staff have received general training regarding providing customer service and comply with dress code requirements.<br />
2 Patients and their families are oriented to their environment upon outpatient visit and admission.<br />
3 The Customer Care Officer provides individualized assistance to address patient and family needs.</p>

<p><strong>NOTE:</strong> Patient orientation includes such things as how to find their way, how to call for assistance, visiting hours, meals, storing personal belongings and where the fire exits are located.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-s787ZdcbKqd-val" name="entryfield" title="RA5.S02.L2" value="[ RA5.S02.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-xO9LxPBWCnH-val" name="entryfield" title="RA5.S02.L2.Sc" value="[ RA5.S02.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The effectiveness of the customer care program is monitored, and actions taken to make improvements.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no monitoring of the effectiveness of the customer care program.<br />
1 The customer care program is monitored through patient feedback (this may be done through the patient satisfaction survey, incident reports, complaints, or other means).<br />
2 Action plans are developed and implemented to improve the program.<br />
3 Leadership demonstrates how they provide recognition for good customer service behaviors.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-yV0JI45DqkL-val" name="entryfield" title="RA5.S02.L3" value="[ RA5.S02.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-HkHPmmd4zAQ-val" name="entryfield" title="RA5.S02.L3.Sc" value="[ RA5.S02.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section2')">Previous</button><button onclick="openTab('section4')">Next</button></div>
</div>
<!-- Section 4: Standard 3 -->

<div class="tab-content" id="section4"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #3: Patient satisfaction monitored&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score">&nbsp;</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-U55aIeI4afb-val" name="entryfield" title="RA5.S03.T" value="[ RA5.S03.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is a policy, procedure, and a tool to monitor patient satisfaction.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy, procedure, or tool to monitor patient satisfaction.<br />
1 A policy and procedure for monitoring patient satisfaction has been developed.<br />
2 A tool has been developed and tested.<br />
3 The required sample size is detailed in the policy and procedure and has been obtained for the targeted populations.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-l731VkKWJEB-val" name="entryfield" title="RA5.S03.L1" value="[ RA5.S03.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-LIojYnOs90l-val" name="entryfield" title="RA5.S03.L1.Sc" value="[ RA5.S03.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Patient satisfaction is monitored, and the data analyzed according to the policy and procedure.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The leaders do not describe an effective patient satisfaction process.<br />
1 Leaders describe an effective patient satisfaction survey process.<br />
2 Data have been collected accurately.<br />
3 Data have been aggregated, analyzed, and displayed according to specific services/departments.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-U15HqAe3yz2-val" name="entryfield" title="RA5.S03.L2" value="[ RA5.S03.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-zoyWBJT4gCC-val" name="entryfield" title="RA5.S03.L2.Sc" value="[ RA5.S03.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Trends in patient satisfaction are used to set priorities for improvement or for further evaluation.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The data have not been used to make improvements.<br />
1 An action plan has been developed to address priority issues identified.</p>

<p>2 Staff interviewed are aware of the patient satisfaction results and the actions being taken.<br />
3 Minutes of meetings show that improvement is being tracked.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-rEFY9ndycHs-val" name="entryfield" title="RA5.S03.L3" value="[ RA5.S03.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-KmeuitYwAgY-val" name="entryfield" title="RA5.S03.L3.Sc" value="[ RA5.S03.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section3')">Previous</button><button onclick="openTab('section5')">Next</button></div>
</div>
<!-- Section 5: Standard 4 -->

<div class="tab-content" id="section5"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #4: Complaint, compliment, and suggestion process&nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-nrULuFx8VP9-val" name="entryfield" title="RA5.S04.T" value="[ RA5.S04.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is a policy and procedure for receiving complaints, compliments, and suggestions.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy and procedure for receiving oral or written complaints, compliments or suggestions or the process is not systematic.<br />
1 There is a policy and procedure for receiving oral or written complaints, compliments or suggestions that includes at least:<br />
&bull; How the hospital informs patients and families about its process to receive and to act on complaints, compliments, and suggestions (this should be publicly available as, for example, posters in the hospital, brochures/handouts, information on the website, etc.)<br />
&bull; The process to be followed when reporting, recording, investigating, and responding to complaints, compliments, and suggestions<br />
&bull; Those who need to be involved in the processes<br />
&bull; Patient and family participation in the processes<br />
&bull; The process to report back to the patient and/or the family within an established time frame</p>

<p>&bull; The process to be followed to address any identified deficiencies in care<br />
&bull; The review of complaints, compliments, and suggestions to identify any common or emerging themes<br />
&bull; The identification of adverse events which should then be managed as an adverse event as well as a complaint<br />
&bull; The acknowledgement of staff who have been complimented<br />
2 Training for all staff, volunteers, contract workers and independent practitioners on the policy and procedure for receiving complaints, compliments and suggestions is included in the hospital&rsquo;s training plan.<br />
3 The process is easily accessible to the public (for example, pencils and paper are available, the email address of the customer service manager is posted in public view or on the website, etc.).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-PcfJSbROL2T-val" name="entryfield" title="RA5.S04.L1" value="[ RA5.S04.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-cXAKrbijeLb-val" name="entryfield" title="RA5.S04.L1.Sc" value="[ RA5.S04.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">An effective process for reviewing and resolving complaints, compliments and suggestions is operational. Feedback is given to affected individuals regarding the process for managing complaints within the specified timeframes.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staff members are unable to describe how they advise patients regarding the complaint, compliment, and suggestion process.<br />
1 Staff members are able to advise the patient and the family about the complaint, compliment, and suggestion process.<br />
2 Staff members describe steps that they take to resolve patient complaints.<br />
3 Staff members refer patients/families according to the policy when they are unable to resolve the patient/family issues.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-jMSiES2g944-val" name="entryfield" title="RA5.S04.L2" value="[ RA5.S04.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-uqeS2WiQE24-val" name="entryfield" title="RA5.S04.L2.Sc" value="[ RA5.S04.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Complaints and suggestions are categorized by type and tracked. This information is used to prioritize patient issues and implement solutions. The results of the solutions are monitored for effectiveness.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Complaint, compliment, and suggestion data are not categorized and trended.<br />
1 Complaint, compliment and suggestion data are aggregated, analyzed and trends identified.<br />
2 Minutes show that complaints, compliments, and suggestions are systematically reviewed within a committee within the specified timeframes.<br />
3 Action plans are developed and implemented to correct recurring problems.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-HHFEatHbmsE-val" name="entryfield" title="RA5.S04.L3" value="[ RA5.S04.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-P46COpZz0u9-val" name="entryfield" title="RA5.S04.L3.Sc" value="[ RA5.S04.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 2 --><!-- Assessor Notes for Standard 2 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S02" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S02" name="notes_RA1_S02" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section4')">Previous</button><button onclick="openTab('section6')">Next</button></div>
</div>
<!-- Section 6: Standard 5 -->

<div class="tab-content" id="section6"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title"><span style="font-size: 14px">STANDARD #5: Clinical outcomes are monitored&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span>Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-OEiDRkW5Aj2-val" name="entryfield" title="RA5.S05.T" value="[ RA5.S05.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Leadership identifies and defines priority clinical outcome indicators.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Indicators have not been established for key clinical outcomes.<br />
1 Indicators have been established by the hospital&rsquo;s leaders for key clinical outcomes that are selected from:<br />
&bull; Clinical guidelines applicable to the patient services provided (mandatory national guidelines are included in this process where available)<br />
&bull; High-volume, high-risk, and high-cost conditions treated at the hospital (including EIDSR, maternal, neonatal and child death, malaria death indicators)<br />
2 Each indicator has a clear definition, formula, data collection method, who is responsible for data collection, frequency of data collection, and target.<br />
3 Data are collected accurately and completely for each of the key clinical outcome indicators.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-FR0tbRGkgHv-val" name="entryfield" title="RA5.S05.L1" value="[ RA5.S05.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-YMRJXv25zS1-val" name="entryfield" title="RA5.S05.L1.Sc" value="[ RA5.S05.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Outcome data are compared to those of previous time periods and published benchmarks (if they exist) and to those of similar organizations (when data is available). Data is used by&nbsp;the facility staff to make improvements in care.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Data for clinical outcome indicators are not aggregated and analyzed.<br />
1 Clinical outcome data are aggregated and analyzed for each of the indicators<br />
2 Death surveillance and audits (especially for maternal, perinatal and neonatal ) are regularly conducted and contributing factors of deaths are communicated to relevant staff (both at the hospital level and referring health centers)<br />
3 The data are compared to established targets and trends over time.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-Ffvz6E5EF9h-val" name="entryfield" title="RA5.S05.L2" value="[ RA5.S05.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-mnJzIfC3SlQ-val" name="entryfield" title="RA5.S05.L2.Sc" value="[ RA5.S05.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The hospital systematically and proactively seeks outcome data from similar organizations and published benchmarks and compares its own performance.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The outcome data are not compared across hospital departments.<br />
1 The data are compared across hospital departments.<br />
2 The data are compared with other hospitals within the country.<br />
3 Some indicator data are compared to published benchmarks, for example, infection rates and a clear implementation plan of recommendations from deaths audit committee established.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-EZ0fFAQ7Su4-val" name="entryfield" title="RA5.S05.L3" value="[ RA5.S05.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-jcCnqNRTR89-val" name="entryfield" title="RA5.S05.L3.Sc" value="[ RA5.S05.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 3 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S03" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S03" name="notes_RA1_S03" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section5')">Previous</button><button onclick="openTab('section7')">Next</button></div>
</div>
<!-- Section 7: Standard 6 -->

<div class="tab-content" id="section7"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #6: Incident, near miss and sentinel event reporting system&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-tK85zxvAbZC-val" name="entryfield" title="RA5.S06.T" value="[ RA5.S06.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Leaders are committed to an incident, near miss and sentinel event reporting process. There is a policy and procedure for the reporting process that clearly defines the incidents, near misses and sentinel events to be reported.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no incident, near miss (including maternal near miss) and sentinel event reporting policy and procedure.<br />
1 An incident, near miss (including maternal near miss) and sentinel event reporting policy and procedure identifies at least:<br />
&bull; Definitions (for example, incident, near miss, sentinel event, just culture, second victim, root cause analysis, ethical dilemma)<br />
&bull; The events to be reported (clinical, administrative, environmental, ethical, etc.)<br />
&bull; The manner in which reporting takes place<br />
&bull; Timeframes for managing incidents, near misses and sentinel events<br />
&bull; The process for conducting root cause analysis/ review of incidents, near misses and sentinel events<br />
&bull;<br />
&bull; The timeframes and processes for informing individuals affected by incidents and sentinel events of the situation and the outcome of the investigation<br />
&bull; The management of staff involved and affected by an incident (just culture, second victims, retraining, and, where required, disciplinary processes)<br />
2 The resources required for implementing the incident, near miss and sentinel event reporting system is included in the hospital&rsquo;s financial planning and reflected in the budget.</p>

<p>3 Training for all staff, volunteers, contract workers and independent practitioners on the incident, near miss and sentinel event reporting system is included in the hospital&rsquo;s training plan.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-ttmIfNgpDK7-val" name="entryfield" title="RA5.S06.L1" value="[ RA5.S06.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-HawqHUDodzp-val" name="entryfield" title="RA5.S06.L1.Sc" value="[ RA5.S06.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The incident, near miss and sentinel event reporting process is implemented, and data are collected.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Few or no incidents or near misses (including maternal near miss) have been reported.<br />
1 Incident, near miss (including maternal death) and sentinel event reports are submitted from each clinical department within the organization and are categorized into types and severity of events, persons involved, and locations.<br />
2 Incidents, near misses and sentinel events are managed according to the processes and timeframes required by the policy and procedure and maternal near misses are reviewed using national tools<br />
3 Individuals affected by the incident and sentinel events are informed of the situation and of the outcome of the investigation within the specified timeframes.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-Qo6uCUXCP0S-val" name="entryfield" title="RA5.S06.L2" value="[ RA5.S06.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-cF2k3tPtaJv-val" name="entryfield" title="RA5.S06.L2.Sc" value="[ RA5.S06.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The data are analyzed and used to educate staff and to improve processes to avoid similar incidents from occurring.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The data are not aggregated, analyzed, and displayed<br />
1 Data related to incident, near misses and sentinel event reporting are aggregated, analyzed, and displayed.<br />
2 Plans are made to reduce the potential for these events recurring, lessons learnt are well documented and accessible to health care providers including maternal services.<br />
3 The results of the interventions are tracked, and actions taken accordingly (PDSA cycle) and lessons learnt from different services including maternal near miss review shared with other hospitals</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-sW92G3o8o14-val" name="entryfield" title="RA5.S06.L3" value="[ RA5.S06.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-rCTprFXKXP1-val" name="entryfield" title="RA5.S06.L3.Sc" value="[ RA5.S06.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 5 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S05" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S05" name="notes_RA1_S05" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section6')">Previous</button><button onclick="openTab('section8')">Next</button></div>
</div>
<!-- Section 8: Standard 7 -->

<div class="tab-content" id="section8"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #7: Staff demonstrate how to improve quality and patient safety&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-WMVVQS2xDJI-val" name="entryfield" title="RA5.S07.T" value="[ RA5.S07.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There are written priorities for staff quality and patient safety training..</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 No priorities have been set for quality and patient safety training.<br />
1 A quality and patient safety training plan lists the priorities.<br />
2 Priorities include training of hospital leaders and different levels of staff.<br />
3 Priorities are based on current need to know information that supports implementation of the quality and patient safety program.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-EfQLRl4DgDh-val" name="entryfield" title="RA5.S07.L1" value="[ RA5.S07.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-SBE6wHSTHPA-val" name="entryfield" title="RA5.S07.L1.Sc" value="[ RA5.S07.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">There is an organized training program for staff who participates in quality improvement and patient safety activities. Department QI teams are carrying out systematic quality improvement activities based on the PDSA model.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no quality and patient safety training program.<br />
1 The quality and patient safety training program includes awareness and quality improvement methods.<br />
2 The training activities are practical and interactive.<br />
3 Training records indicate that the targeted groups attend the training activities.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-jmLCDrSduWl-val" name="entryfield" title="RA5.S07.L2" value="[ RA5.S07.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-rqnrukoL8TE-val" name="entryfield" title="RA5.S07.L2.Sc" value="[ RA5.S07.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The impact and effectiveness of the training program are documented and used to improve program content and scope over time.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The knowledge of trained staff members regarding quality and patient safety is not evaluated.<br />
1 The knowledge of staff members attending quality training is evaluated.<br />
2 The skills of staff attending quality training are evaluated.<br />
3 The application of the skills to practice is evaluated.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-nTta26qJyfK-val" name="entryfield" title="RA5.S07.L3" value="[ RA5.S07.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-YW42DnqSD8E-val" name="entryfield" title="RA5.S07.L3.Sc" value="[ RA5.S07.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 7 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S07" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S07" name="notes_RA1_S07" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section7')">Previous</button><button onclick="openTab('section9')">Next</button></div>
</div>
<!-- Section 9: Standard 8 -->

<div class="tab-content" id="section9"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #8: Communicating quality and patient safety information to staff&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-WnyNm3z4rOQ-val" name="entryfield" title="RA5.S08.T" value="[ RA5.S08.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">The means of communicating quality and patient safety information to staff is described in the quality plan.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The quality plan does not include expectations regarding when quality reports are to be submitted.<br />
1 The quality plan identifies when the various departments/committees are to submit quality reports to the Quality Improvement Committee.<br />
2 The plan identifies when and how information flows between the leadership, departments, and staff.<br />
3 The plan identifies how training for all staff, volunteers, contract workers and independent practitioners on quality and patient safety is included in the hospital&rsquo;s training plan.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-JH4uyb3YGUq-val" name="entryfield" title="RA5.S08.L1" value="[ RA5.S08.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-fVuraWoSEWQ-val" name="entryfield" title="RA5.S08.L1.Sc" value="[ RA5.S08.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Quality and patient safety information are regularly communicated to staff.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Quality and patient safety information is not consistently communicated to staff during departmental staff meetings.<br />
1 Staff meeting minutes show that quality and patient safety information is communicated monthly in the departments.<br />
2 Training registers show that staff, volunteers, contract workers and independent practitioners were trained on quality and patient safety as set out in the hospital&rsquo;s training plan<br />
3 Staff interviewed are able to describe quality and safety activities performed within the department in the last 3 months.</p>

<p>&nbsp;</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-j5MJFah6DXL-val" name="entryfield" title="RA5.S08.L2" value="[ RA5.S08.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-ETIlL8N6d2H-val" name="entryfield" title="RA5.S08.L2.Sc" value="[ RA5.S08.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Staff use of quality and patient safety information is evaluated to improve the effectiveness of the communication effort.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Meeting effectiveness is not evaluated quarterly in the departments.<br />
1 An evaluation of meeting effectiveness is conducted at least quarterly for all departmental staff meetings.<br />
2 The results of the feedback are shared with each group.<br />
3 An action plan is developed for each department to improve information sharing and use of quality information.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-HmXhdIzFBDb-val" name="entryfield" title="RA5.S08.L3" value="[ RA5.S08.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-z9VmP2hyh4c-val" name="entryfield" title="RA5.S08.L3.Sc" value="[ RA5.S08.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 8 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S08" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S08" name="notes_RA1_S08" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section8')">Previous</button><button onclick="openTab('section10')">Next</button></div>
</div>
<!-- Section 10: Standard 9 -->

<div class="tab-content" id="section10"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #9: Staff satisfaction monitored&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="qs6bBq3PFUk-EzFUFMPDTp2-val" name="entryfield" title="RA5.S09.T" value="[ RA5.S09.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">There is a policy, procedure, and tool to monitor staff satisfaction.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy, procedure, or tool to monitor staff satisfaction.<br />
1 A policy and procedure for monitoring staff satisfaction has been developed.<br />
2 A tool has been developed and tested.<br />
3 A sufficient sample size has been obtained (at least 50% of all staff members).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-sV6NSNY7GhB-val" name="entryfield" title="RA5.S09.L1" value="[ RA5.S09.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-eBXnwaPaxX4-val" name="entryfield" title="RA5.S09.L1.Sc" value="[ RA5.S09.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Staff satisfaction is monitored according to the policy and procedure, and the data analyzed and reported to staff. An improvement plan is developed and implemented.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staff satisfaction data have not been collected.<br />
1 An annual hospital staff satisfaction survey is conducted.<br />
2 Data have been collected accurately.<br />
3 Data have been aggregated, analyzed, and displayed according to specific services/departments.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-zLvD8Bt2ySN-val" name="entryfield" title="RA5.S09.L2" value="[ RA5.S09.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-CRRFWpyDAyx-val" name="entryfield" title="RA5.S09.L2.Sc" value="[ RA5.S09.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Trends in staff satisfaction are used to set priorities for improvement or for further evaluation.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The results of the staff satisfaction have not been shared with the staff.<br />
1 Staff meeting minutes show that the outcomes of the survey are made known to staff.<br />
2 An action plan has been developed to address priority issues identified.<br />
3 The action plan has been implemented, progress is being tracked and the impact is measured.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="qs6bBq3PFUk-vnX13iqyuEy-val" name="entryfield" title="RA5.S09.L3" value="[ RA5.S09.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="qs6bBq3PFUk-lVfsdWVdZ0a-val" name="entryfield" title="RA5.S09.L3.Sc" value="[ RA5.S09.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 9 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S09" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S09" name="notes_RA1_S09" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section9')">Previous</button><button onclick="openTab('section11')">Back to Start</button></div>
</div>
</div>
</div>
<script>
    // Function to open a tab
    function openTab(tabId) {
        var i;
        var x = document.getElementsByClassName("tab-content");
        var tabButtons = document.getElementsByClassName("tab-button");

        // Hide all tab contents and remove active class from all buttons
        for (i = 0; i < x.length; i++) {
            x[i].style.display = "none";
            tabButtons[i].classList.remove("active");
        }

        // Display the selected tab content and add the active class to the corresponding button
        document.getElementById(tabId).style.display = "block";
        document.getElementById(tabId).classList.add("active");

        // Add active class to the tab button corresponding to the selected tab content
        for (i = 0; i < tabButtons.length; i++) {
            if (tabButtons[i].getAttribute("onclick").includes(tabId)) {
                tabButtons[i].classList.add("active");
            }
        }
    }

    // Function to sync the Score input value to the Overall Score input
    function syncOverall(scoreId, overallId) {
        var scoreElem = document.getElementById(scoreId);
        var overallElem = document.getElementById(overallId);
        if (scoreElem && overallElem) {
            overallElem.value = scoreElem.value;
        }
    }
</script>