<meta charset="UTF-8" /><meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title></title>
<style type="text/css">/* * {
  border: red 1px solid;
} */

  body {
    font-family: "Poppins", Arial, sans-serif;
    background-color: #f7f9fc;
    margin: 0;
    padding: 0;
    color: #003764;
  }

  input[type='text'] {
    color: red;
    width: 50px;
    align-items: center;
  }

 
  .container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
  }

  .tabs {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    background-color: #003764;
    border-radius: 5px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: auto;
  }

  .tab-button {
    flex: 1;
    padding: 10px 15px;
    margin: 5px;
    color: white;
    background-color: #003764;
    border: none;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    border-radius: 5px;
    white-space: nowrap;
  }

  .tab-button:hover,
  .tab-button.active {
    background-color: #48a849;
    transform: scale(1.05);
    color: white;
  }

  .tab-content {
    width: 100%;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    animation: fadeEffect 0.5s;
    box-sizing: border-box;
    display: block;
    /* here is whre you change the visibilti */
  }

  .tab-content.active {
    display: block;
  }

  @keyframes fadeEffect {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  h2,
  h3,
  h4 {
    text-align: center;
    color: #003764;
    margin-bottom: 20px;
  }

  /* New div-based table styles */
  .standard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #003764;
    color: white;
    border-radius: 5px 5px 0 0;
    margin-bottom: 0;
    padding: 10px 15px;
    font-weight: bold;
    gap: 50px;
  }

  .standard-header-title {
    flex: 2;
    font-weight: bold;
    font-size: 16px;
  }

  .standard-header-score {
    width: 70px;
  }

  .standard-header-score input {
    width: 70px;
    text-align: center;
  }

  .div-table {
    width: 100%;
    margin: 20px 0;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .div-table-header {
    display: flex;
    background-color: #003764;
    color: white;
    font-weight: 600;
    font-size: 14px;
    padding: 10px;
    text-transform: uppercase;
  }

  .div-table-header-col1 {
    width: 25%;
  }

  .div-table-header-col2 {
    width: 45%;
  }

  .div-table-header-col3,
  .div-table-header-col4 {
    width: 15%;
    text-align: center;
  }

  .div-table-row {
    display: flex;
    border-bottom: 1px solid #eee;
  }

  .div-table-row:nth-child(even) {
    background: #f7f9fc;
  }

  .div-table-cell {
    padding: 10px;
  }

  .div-table-cell-col1 {
    width: 25%;
    background-color: white;
  }

  .div-table-cell-col2 {
    width: 45%;
  }

  .div-table-cell-col3 {
    width: 15%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .div-table-cell-col4 {
    width: 15%;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .level-row {
    border-bottom: 1px solid #eee;
  }

  .level-heading {
    font-weight: bold;
    margin-bottom: 10px;
  }

  .level-description {
    margin-bottom: 10px;
  }

  .navigation-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
  }

  .navigation-buttons button {
    padding: 8px 16px;
    background-color: #003764;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .navigation-buttons button:hover {
    background-color: #48a849;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .div-table-row {
      flex-direction: column;
    }

    .div-table-cell-col1,
    .div-table-cell-col2,
    .div-table-cell-col3,
    .div-table-cell-col4 {
      width: 100%;
    }

    .div-table-header {
      display: none;
    }

    .tabs {
      justify-content: center;
    }

    .tab-button {
      font-size: 12px;
      flex: none;
      padding: 8px;
    }

    .tab-content {
      padding: 15px;
    }
  }
</style>
<div class="container"><!-- Tabs Navigation -->
<div class="tabs"><button class="tab-button active" onclick="openTab('section1')">RISK AREA 2</button><button class="tab-button" onclick="openTab('section2')">Standard 1</button><button class="tab-button" onclick="openTab('section3')">Standard 2</button><button class="tab-button" onclick="openTab('section4')">Standard 3</button><button class="tab-button" onclick="openTab('section5')">Standard 4</button><button class="tab-button" onclick="openTab('section6')">Standard 5</button><button class="tab-button" onclick="openTab('section7')">Standard 6</button><button class="tab-button" onclick="openTab('section8')">Standard 7</button><button class="tab-button" onclick="openTab('section9')">Standard 8</button><button class="tab-button" onclick="openTab('section10')">Standard 9</button><button class="tab-button" onclick="openTab('section11')">Standard 10</button></div>

<div class="tab-content active" id="section1">
<h2><strong>RISK AREA #2 &ndash; COMPETENT AND CAPABLE WORKFORCE</strong></h2>

<p>Patients assume that the health care professionals providing their care and treatment are competent and capable. Furthermore, even though health care professionals may intend to provide quality and safe patient care every day, they are frequently not supported by consistent and low-risk processes and systems, thus placing patients at risk. Many health care professionals, such as physicians, traditional care providers, and others, are permitted by law or regulation to work without supervision and thus without some of the checks and balances that reduce risk. It is essential that all health care professionals have appropriate and valid credentials and are competent to provide care and treatment to patients.</p>

<p><br />
A primary activity related to a competent and capable workforce is an appropriate orientation and ongoing education in patient risk areas. These include a general orientation to the organization such as information on infection control, hazardous materials management, and others. In addition, staff must be oriented to the specific department requirements. It is also critical that staff members know how to communicate essential patient information from one person to another and from one care unit to another. The criteria below address risk points in workforce management.</p>
<!-- Required Documents Section -->

<div style="margin-left: 10px; margin-top: 50px">
<h3><strong>Required Documents</strong></h3>

<p style="margin-left: 160px">1. Staffing plan<br />
2. Training and competency assessment plan<br />
3. Staff member job descriptions<br />
4. Personnel file policies and procedures<br />
5. Credentialing policies and procedures<br />
6. Privileging policy and procedure<br />
7. Staff general orientation program agenda<br />
8. Departmental orientation checklists<br />
9. List of trainees and assignments<br />
10. Cardio-pulmonary resuscitation training policy<br />
11. Cardio-pulmonary resuscitation policy and procedure for providing resuscitation services<br />
12. Training and competency assessment records as evidence of meeting various standards<br />
13. Staff health and safety policies and procedures<br />
14. Occupational hazard assessment</p>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section2')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>

<div class="allStandards"><!-- Section 2: Standard 1 -->
<div class="tab-content" id="section2"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #1: Personnel files available, complete, and up to date&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-t8pgVN2CF0b-val" name="entryfield" title="RA2.S01.T" value="[ RA2.S01.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Policies describe the content that is to be included in the personnel file and job descriptions.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p style="margin-left: 40px;">0 A policy describing the contents of personnel files is not written and/or does not contain the required elements.<br />
1 A current policy outlines the content of personnel files and includes:</p>

<ol>
	<li style="margin-left: 40px;">Current job description</li>
	<li style="margin-left: 40px;">Curriculum vitae</li>
	<li style="margin-left: 40px;">Copies of required credentials including degrees/diplomas, evidence of registration certificates and current license (if applicable)</li>
	<li style="margin-left: 40px;">List of privileges (if applicable)</li>
	<li style="margin-left: 40px;">Evidence of completion of resuscitation training (if applicable)</li>
	<li style="margin-left: 40px;">Performance evaluation (PBF) and (MIFOTRA) as prescribed by current policy</li>
	<li style="margin-left: 40px;">Training certificates</li>
	<li style="margin-left: 40px;">Evidence of completion of orientation within two months of appointment (new personnel only)</li>
	<li style="margin-left: 40px;">Required health assessments/vaccinations</li>
	<li style="margin-left: 40px;">Disciplinary action reports</li>
</ol>

<p style="margin-left: 40px;">2 A current policy indicates that the job description contains at least:</p>

<ol>
	<li style="margin-left: 40px;">Education, training and experience required</li>
	<li style="margin-left: 40px;">Reporting relationship (who they report to)</li>
	<li style="margin-left: 40px;">Roles and responsibilities</li>
	<li style="margin-left: 40px;">PBF contract (motivation)</li>
	<li style="margin-left: 40px;">Job contract</li>
</ol>

<p style="margin-left: 40px;">3 A policy describes the content of volunteer and contracted personnel files.</p>

<ol>
	<li style="margin-left: 40px;">Copy of contract</li>
	<li style="margin-left: 40px;">Qualifications (education, training, and experience)</li>
	<li style="margin-left: 40px;">cCurrent professional license (if indicated)</li>
	<li style="margin-left: 40px;">Proof of orientation</li>
	<li style="margin-left: 40px;">Required health assessments/vaccinations</li>
</ol>

<p style="margin-left: 40px;"><strong>NOTE:</strong></p>

<ul>
	<li style="margin-left: 40px;">Orientation records are only expected for personnel that have been hired within the past twelve months.</li>
	<li style="margin-left: 40px;">Other forms of documentation would be acceptable, for example, computerized list of staff that participated in an activity with dates of the activity and the providers. This would include training activities and lists of vaccinations.</li>
</ul>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-QKx4xt65Jed-val" name="entryfield" title="RA2.S01.L1" value="[ RA2.S01.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-KJajvHT0d7H-val" name="entryfield" title="RA2.S01.L1.Sc" value="[ RA2.S01.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Personnel files are filed in a standardized order and contain all required elements as described in the policy.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 A process for ensuring that all healthcare professionals remain registered in accordance with legal requirements is not in place.<br />
1 A process for ensuring that all healthcare professionals remain registered in accordance legal requirements is in place and personnel files reviewed had evidence of a current license (when required).<br />
2 Personnel, volunteer and contractor worker files contained the required items.<br />
3 Personnel, volunteer and contractor workers files were arranged in an organized standard format.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-ix7ncwgK5Am-val" name="entryfield" title="RA2.S01.L2" value="[ RA2.S01.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-tTkna8MyLVA-val" name="entryfield" title="RA2.S01.L2.Sc" value="[ RA2.S01.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<p class="level-description" style="text-align: center;"><strong>Level 3:</strong></p>

<p class="level-description">A process is in place to manage personnel files.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy and procedure for the management of personnel files.<br />
1 A policy and procedure is implemented for the management of personnel files that includes at least:</p>

<ul>
	<li>A personnel record audit to ensure that records are up to date</li>
	<li>Authority to access personnel records</li>
	<li>Secure storage of personnel records</li>
</ul>

<p>2 Performance-based finance (PBF) reviews, conducted within the past six months, are present in the personnel files.<br />
3 The online Human Resource Information System is available and updated with a current list of quarterly payments of salary and PBF.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-JlnvieXLlsv-val" name="entryfield" title="RA2.S01.L3" value="[ RA2.S01.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-rR28zX4ubBM-val" name="entryfield" title="RA2.S01.L3.Sc" value="[ RA2.S01.L3.Sc ]" /></div>
</div>
<!-- Additional row for assessor notes -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section1')">Previous</button><button onclick="openTab('section3')">Next</button></div>

<div class="navigation-buttons">&nbsp;</div>
</div>
<!-- Section 3: Standard 2 -->

<div class="tab-content" id="section3"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #2: Credentials of healthcare professionals &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-NelOzJUNIf5-val" name="entryfield" title="RA2.S02.T" value="[ RA2.S02.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure describes a uniform process for gathering and verifying the credentials of healthcare professionals (including independent clinical practitioners and volunteers) and assigning responsibilities accordingly.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy and procedure for gathering and verifying the credentials of healthcare professionals.<br />
1 A current policy and procedure details:<br />
a. The required registration and certification with professional councils (including areas of specialization)<br />
b. The required licensure, education, training, and competence<br />
c. A uniform process for gathering the credentials of healthcare professionals (including independent clinical practitioners and volunteers)<br />
d. The process for conducting verification of credentials<br />
e. The process for appointing healthcare professionals based on credentials<br />
f. How healthcare professionals are assigned job responsibilities based on their credentials and when extending their scope of services (task shifting).</p>

<p>2 Appointments are not made, and direct patient care is not provided until at least licensure/registration of healthcare professionals are verified.<br />
3 Healthcare professionals that are extending their scope of services (task-shifting) have associated competency levels defined and assessed, which are documented in the majority of personnel files.<br />
NOTE: The primary source verification may be conducted by the professional council, however, the policies and procedures need to indicate that this is the process.<br />
For file review, identify the required credentials in the job descriptions of selected healthcare professionals and compare them to the actual credentials in the personnel file.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-f4QNncYQsLy-val" name="entryfield" title="RA2.S02.L1" value="[ RA2.S02.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-PXAozYE87OA-val" name="entryfield" title="RA2.S02.L1.Sc" value="[ RA2.S02.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The credentials are gathered and verified according to the policy and procedure. and healthcare professionals are assigned roles and responsibilities based on the credentials.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 A complete set of required credentials is not maintained for each healthcare professional.<br />
1 All credentials required are copied by the hospital and maintained for each healthcare professional in their personnel files.<br />
2 When work responsibilities require specialized qualifications for healthcare professionals (for example, surgery, obstetrics, ICU, etc.), verification (by the professional council) of training in that specialty is documented in the personnel file.<br />
3 The credentials of independent clinical practitioners and volunteers providing clinical care are verified before providing direct patient care.<br />
NOTE: For file review select healthcare professionals that are working in areas that require specific qualifications, for example, specialized surgery, ICU, ED, dialysis, pathology, radiology, etc.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-dsIEy6r3xNf-val" name="entryfield" title="RA2.S02.L2" value="[ RA2.S02.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-EYjpMe4imWA-val" name="entryfield" title="RA2.S02.L2.Sc" value="[ RA2.S02.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Evidence shows that the credentialing process is effective.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no data that show that the verification process is carried out according to the policy and procedure.<br />
1 A dated and signed document indicating that verification of credentials has been done for each healthcare professional is present.<br />
2 A document is present showing that the hospital verifies that the 3rd party (professional council) implements the verification process described in the policy and procedure.<br />
3 Audits are conducted to ensure that healthcare professional appointments are made according to hospital policy.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-ZYxkoJMB0Ug-val" name="entryfield" title="RA2.S02.L3" value="[ RA2.S02.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-FEIXP3hvLFt-val" name="entryfield" title="RA2.S02.L3.Sc" value="[ RA2.S02.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section2')">Previous</button><button onclick="openTab('section4')">Next</button></div>
</div>
<!-- Section 4: Standard 3 -->

<div class="tab-content" id="section4"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #3: Privileges for Health Professionals&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score">&nbsp;</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-QUjJeRz9wAs-val" name="entryfield" title="RA2.S03.T" value="[ RA2.S03.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure describe a standardized process to grant clinical privileges to health professionals and assign job responsibilities accordingly.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no committee that manages the credentialing and privileging of health professionals at the hospital.<br />
1 The committee&rsquo;s members in charge of examining applications for privileging have been appointed and are meeting according to the ministerial instructions establishing it.<br />
2 A policy and procedure describes a standardized process for approving clinical privileges, including the process for approving special and temporary privileges and the training, experience and competence required for new procedures; and how the credentials are used to assign job responsibilities.<br />
3 A core set of privileges is defined for categories of practitioners, for example, internists and general practitioners, including task-shifting for any other health professional.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-O5pu4BiLK2l-val" name="entryfield" title="RA2.S03.L1" value="[ RA2.S03.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-YlZcCWk7swH-val" name="entryfield" title="RA2.S03.L1.Sc" value="[ RA2.S03.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The organization uses a standardized procedure to approve privileges on initial appointment and when new skills have been acquired to each type of health professional listed in the policy and procedure. The patient services to be provided by each health&nbsp;professional are clearly delineated and communicated by hospital leaders across the organization and to the practitioner.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 A process for approving health professional. privileges in not in place or is inconsistently applied.<br />
1 Each health professional has defined core privileges and special privileges (for example, task-shifting, general practitioner may perform hysterectomy), with evidence of training/experience to perform the special procedure, documented in the personnel file, which has been updated within the past 24 months.</p>

<p>2 . Privileges are communicated to relevant departments through a written document.<br />
3 .A personnel file is kept for practitioners given temporary privileges (for example, visiting foreign surgeons) that includes:<br />
&bull; Licensure status<br />
&bull; Written request<br />
&bull; Verified information supports a favorable determination regarding the requesting practitioner&rsquo;s qualifications and ability to exercise the requested privileges.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-epjx8hQDQNA-val" name="entryfield" title="RA2.S03.L2" value="[ RA2.S03.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-DBvrOzkkUyb-val" name="entryfield" title="RA2.S03.L2.Sc" value="[ RA2.S03.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Each privileged practitioner provides only those services that have been specifically permitted by the hospital. The medical staff leaders can demonstrate how the procedure was effective in the appointment process.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no or inconsistent evidence of monitoring professional practice.<br />
1 All health professionals are included in the monitoring and evaluation of professional practice (These may be included in the performance appraisal process; indicators may include complication rates and compliance with clinical practice guidelines.<br />
2 Areas of achievement and potential improvement related to behaviors and clinical results are documented in the personnel file.<br />
3 Findings are used for determining privileges and are reflected in the list of privileges (for example, if a physician has had surgical complications, their privileges for surgery may be changed.)</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-lKjSHCMbl3W-val" name="entryfield" title="RA2.S03.L3" value="[ RA2.S03.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-oWDFyluC9Di-val" name="entryfield" title="RA2.S03.L3.Sc" value="[ RA2.S03.L3.Sc ]" /></div>
</div>

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_Sxx_L3" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_Sxx_L3" name="notes_RA1_S01" rows="3" style="
                width: 100%;
                border: 1px solid #ccc;
                border-radius: 5px;
                resize: vertical;
              "></textarea></div>
</div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section3')">Previous</button><button onclick="openTab('section5')">Next</button></div>
</div>
<!-- Section 5: Standard 4 -->

<div class="tab-content" id="section5"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #4: Orientation to hospital and jobs&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-A6zNC7gifBc-val" name="entryfield" title="RA2.S04.T" value="[ RA2.S04.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure for general overall hospital and job-specific orientation for new, and reassigned staff, volunteers, contracted workers, and independent practitioners is available.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no orientation policy and procedure, or it does not address general and job specific orientation expectations and/or all types of staff listed in the standard.<br />
1 A policy and procedure describes orientation expectations for new and reassigned staff, volunteers, contracted workers, and independent practitioners.<br />
2 The general orientation program includes initial orientation on:<br />
a. Risk management<br />
b. Patient safety<br />
c. Infection control<br />
d. Incident reporting<br />
e. Quality improvement<br />
f. Identified policies and procedures<br />
3 A job-specific orientation program is developed for each type of position within a department/service and includes an initial competency assessment.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-IZVOVLYu4eg-val" name="entryfield" title="RA2.S04.L1" value="[ RA2.S04.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-vcRKVmQr5uu-val" name="entryfield" title="RA2.S04.L1.Sc" value="[ RA2.S04.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">General and specific job orientation is provided for all new, reassigned staff,&nbsp;volunteers, contracted workers, and independent practitioners.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The orientation programs are not implemented.<br />
1 Staff members hired within the past six months have attended general orientation and it is documented on a training register or other records&nbsp;</p>

<p>2 Staff members hired or transferred within the past six months have completed a documented, job-specific orientation and competency assessment.<br />
3 General orientation of new contracted workers, volunteers and independent practitioners is conducted within 2 months and the orientation report is retained in their individual files.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-weV4GrVmR57-val" name="entryfield" title="RA2.S04.L2" value="[ RA2.S04.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-LQ7BKVshf5F-val" name="entryfield" title="RA2.S04.L2.Sc" value="[ RA2.S04.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The implementation and effectiveness of the orientation program is monitored and improved upon when required.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The orientation program is not monitored.<br />
1 A process is in place to verify whether all new and reassigned staff have completed the general and job specific orientation programs as planned.<br />
2 A process is in place to verify whether all contracted workers, volunteers and independent practitioners have received orientation.<br />
3 The data gathered is used to make improvements in the orientation program.<br />
<strong>NOTE:</strong> The orientation may be conducted by the contractor, given that the program content is consistent with the expectations of the hospital, for example, infection prevention and control (IPC) policies/procedures, fire safety plan. In this case, the contractor needs to provide documentation that the orientation was provided, and this document is kept on file.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-QZQpoRlz6Iv-val" name="entryfield" title="RA2.S04.L3" value="[ RA2.S04.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-eqhiHMPFVuz-val" name="entryfield" title="RA2.S04.L3.Sc" value="[ RA2.S04.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 2 --><!-- Assessor Notes for Standard 2 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S02" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S02" name="notes_RA1_S02" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section4')">Previous</button><button onclick="openTab('section6')">Next</button></div>
</div>
<!-- Section 6: Standard 5 -->

<div class="tab-content" id="section6"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title"><span style="font-size: 14px">STANDARD #5: Trained and competent staff&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span>Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-RjxYSuuJNZS-val" name="entryfield" title="RA2.S05.T" value="[ RA2.S05.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">The hospital has developed a training and competency assessment plan to ensure that staff knowledge and skills are consistent with patient needs.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no staff training and competency assessment plan and/or it is not based on identified training needs.<br />
1 The hospital has an annual written hospital staff training and competency assessment plan based on:<br />
&bull; The initial training and competency requirements of new staff<br />
&bull; Ongoing competency assessment requirements for all staff<br />
&bull; The training requirements of staff who are appointed in leadership positions (for example, management training, financial management training, human resource training, etc.)<br />
&bull; Identified ongoing training needs (for example, infection prevention and control, risk management, incident reporting and management, fire and evacuation, resuscitation, person-centered care, etc.)<br />
&bull; Training needs in identified high priority services (for example, maternal, newborn, child and adolescent health, HIV, TB, Malaria etc.)<br />
&bull; Monitoring data from hospital management processes<br />
&bull; The introduction of new equipment and technology</p>

<p>&bull; Deficiencies in skills, knowledge and competency identified through the performance evaluation and review process<br />
&bull; New clinical processes<br />
&bull; Updating or acquiring new skills and knowledge<br />
&bull; Plans and development strategies of the facility<br />
2 All levels of staff members are included in the plan.<br />
3 Managers develop annual department/service-specific staff training and competency assessment plans to meet the needs of their patients/departments.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-ceEKZh32bJ0-val" name="entryfield" title="RA2.S05.L1" value="[ RA2.S05.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-VVwpd5dra6s-val" name="entryfield" title="RA2.S05.L1.Sc" value="[ RA2.S05.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Training and competency assessment is carried out to meet the educational needs of staff.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Training and competency assessment activities have not been carried out as planned.<br />
1 Records of training and competency assessment activities and attendance are maintained.<br />
2 Hospital training and competency assessment activities have been conducted as planned.<br />
3 Presentations are held quarterly and documented regarding case management and current medical articles for healthcare professionals.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-Baoq85Z14lv-val" name="entryfield" title="RA2.S05.L2" value="[ RA2.S05.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-BiCE5JZUkIP-val" name="entryfield" title="RA2.S05.L2.Sc" value="[ RA2.S05.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The effectiveness of staff training and competency assessment is monitored.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staff training and competency assessment effectiveness is not evaluated.<br />
1 Minutes of staff meetings show that staff who received training outside the hospital share the learning with other staff in the hospital (for example, content outline, handouts used).<br />
2 Hospital staff training and competency assessment activities are monitored for effectiveness.<br />
3 The monitoring data is analyzed and used to improve the effectiveness of training and competency assessment.<br />
NOTE: Effectiveness can be measured by return demonstration of skills or linked with quality monitoring, for example, improved documentation, hand washing or adherence to policies/procedures or protocols. Staff satisfaction with the training activity is not the intended measure of effectiveness for this standard.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-b3iIrjDne1P-val" name="entryfield" title="RA2.S05.L3" value="[ RA2.S05.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-Ut1ZNIRxH7R-val" name="entryfield" title="RA2.S05.L3.Sc" value="[ RA2.S05.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 3 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S03" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S03" name="notes_RA1_S03" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section5')">Previous</button><button onclick="openTab('section7')">Next</button></div>
</div>
<!-- Section 7: Standard 6 -->

<div class="tab-content" id="section7"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #6: Sufficient staff to meet patient needs&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-xNFuC2dxhG9-val" name="entryfield" title="RA2.S06.T" value="[ RA2.S06.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">Staffing plans are written in each department that identifies the number of staff needed per shift considering the size of the hospital, the scope of services provided and the workload.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staffing plans have not been developed.<br />
1 Each department has a staffing plan, based on the hospital general staffing plan.<br />
2 When staffing levels do not meet the needs, policies and procedures are in place that describe actions to be taken, for example, reassign staff, on-call staff.<br />
3 Staffing plans are based on a recognized and agreed upon staffing model (for example, WHO&rsquo;s Workload Indicators of Staffing Needs or a national staffing model) that includes at least:<br />
&bull; Number and types of required staff<br />
&bull; Knowledge and skills relevant to the service being provided<br />
&bull; Patient acuity (in clinical areas)<br />
&bull; The specialized staffing needs of identified priority services (for example, maternal, newborn, child and adolescent health)<br />
&bull; Workload (for example, nurse to patient ratio, number of tests performed, or number of rooms to be cleaned)</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-NWFg3Ha6flO-val" name="entryfield" title="RA2.S06.L1" value="[ RA2.S06.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-P8iVsUYhEmR-val" name="entryfield" title="RA2.S06.L1.Sc" value="[ RA2.S06.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The work schedule provides an adequate number of staff (according to the plan) on each shift to meet the departmental needs.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 An interview with department heads indicates that schedules are not developed based on the staffing plan.<br />
1 Staffing schedules in the departments are filled out according to the plans.</p>

<p>2 Staffing schedules in the departments are filled out according to the plans; but the number of staff that actually worked is not consistent with the plan.<br />
3 Staffing schedules are filled out according to the plan and the number of staff that actually worked is consistent with the plan.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-IImppdGVsWx-val" name="entryfield" title="RA2.S06.L2" value="[ RA2.S06.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-WuyXa51YZMz-val" name="entryfield" title="RA2.S06.L2.Sc" value="[ RA2.S06.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Staffing plans are evaluated to determine whether adequate staffing is provided; when shortages exist, leaders set priorities and make adjustments to provide safe care.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no evaluation of the staffing plans.<br />
1 A monthly review of data regarding staffing planned in relation to the staff that worked is done in each department.<br />
2 When interviewed, department leaders are able to describe how they effectively manage situations in which staffing needs are not met, which is consistent with the policy and procedure.<br />
3 Workload studies are done to evaluate the staffing needs.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-CsTMQhvxOrI-val" name="entryfield" title="RA2.S06.L3" value="[ RA2.S06.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-xFgauNF9AHM-val" name="entryfield" title="RA2.S06.L3.Sc" value="[ RA2.S06.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 5 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S05" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S05" name="notes_RA1_S05" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section6')">Previous</button><button onclick="openTab('section8')">Next</button></div>
</div>
<!-- Section 8: Standard 7 -->

<div class="tab-content" id="section8"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #7: Oversight of students/trainees&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-YtkXk2udIOs-val" name="entryfield" title="RA2.S07.T" value="[ RA2.S07.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A current policy and procedure is available on student oversight.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 A current policy and procedure regarding student oversight is not present.<br />
1 A current policy and procedure is available on student oversight.<br />
2 A current list of trainees and their assignments is present for each type of program.<br />
3 A list of trainees and their assignments are observed to be posted within relevant units.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-LBnws4a18B4-val" name="entryfield" title="RA2.S07.L1" value="[ RA2.S07.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-QQ1xXu9sY5R-val" name="entryfield" title="RA2.S07.L1.Sc" value="[ RA2.S07.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The number of trainees and their assignments are known. The current competence (level of training) of each trainee is known, which is used to make assignments and indicate level of required supervision.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Information about the competency levels of trainees is not available.<br />
1 Information about the competency levels of trainees is available.<br />
2 Information about the competence level of trainees is made available to the heads of the departments where trainees are allocated.<br />
3 An interview with department heads demonstrates that trainees are assigned patient care consistent with their competency level and clinical oversight is planned in advance.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-gNDQy3NrgJz-val" name="entryfield" title="RA2.S07.L2" value="[ RA2.S07.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-y1yFihCThn7-val" name="entryfield" title="RA2.S07.L2.Sc" value="[ RA2.S07.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">Monitoring is performed to determine whether the oversight of students is in compliance with the policy and procedure.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no evidence that the trainees are supervised according to the policy and procedure.<br />
1 The trainee assignment form indicates that all students are supervised according to policy.<br />
2 Orientation records show that all trainees are oriented to the facility&rsquo;s quality and safety policies and procedures.<br />
3 Department records show that student oversight is routinely performed.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-lYZ1WdKS5nP-val" name="entryfield" title="RA2.S07.L3" value="[ RA2.S07.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-IhJb5Ihi3e9-val" name="entryfield" title="RA2.S07.L3.Sc" value="[ RA2.S07.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 7 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S07" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S07" name="notes_RA1_S07" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section7')">Previous</button><button onclick="openTab('section9')">Next</button></div>
</div>
<!-- Section 9: Standard 8 -->

<div class="tab-content" id="section9"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #8: Training in resuscitative techniques&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-IEpiNqi8ivB-val" name="entryfield" title="RA2.S08.T" value="[ RA2.S08.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure defines the staff that are required to be trained and at which level (for example, basic CPR, BLS, ACLS, PALS or NRP). A resuscitation policy and procedure describes how to respond to a resuscitation emergency.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The policies and procedures are not written regarding resuscitation.<br />
1 A policy describes the CPR training requirements and level for each category of staff (basic, advanced, pediatric, neonatal and trauma).<br />
2 A policy and procedure includes:<br />
a. Responsibilities for each responder, for example, CPR, obtaining emergency medications/equipment, administering medications, recording the events, and airway management<br />
b. How to call the resuscitation team<br />
c. Personnel that are to respond<br />
d. Documentation required<br />
e. Evaluation of the resuscitation response<br />
3 An annual resuscitation training plan is present that includes:<br />
a. Identification of staff to be trained<br />
b. Priority staff to be trained<br />
c. Type of training activity<br />
d. Number of personnel to attend (at least 25% of relevant staff in first year and increment of 25% each subsequent year)</p>

<p>e. The certified training provider<br />
f. Timeframes<br />
g. Ongoing competency assessment (for example, by conducting mock resuscitation drills).</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-JLNLOznMI1X-val" name="entryfield" title="RA2.S08.L1" value="[ RA2.S08.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-NpG5hHlDJv1-val" name="entryfield" title="RA2.S08.L1.Sc" value="[ RA2.S08.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">Staff members have successfully completed competency-based training by a qualified instructor and have been retrained within the last two years according to the policy.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Staff training in resuscitation is not proceeding as planned.<br />
1 Relevant staff have documentation of training for their roles during resuscitation according to the resuscitation policy and procedure.<br />
2 The resuscitation training is being carried out according to the plan.<br />
3 Staff that have attended training have documentation of the required level of competency according to the policy and procedure.</p>

<p>&nbsp;</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-GNlY3gALF94-val" name="entryfield" title="RA2.S08.L2" value="[ RA2.S08.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-zpchVjcuFAt-val" name="entryfield" title="RA2.S08.L2.Sc" value="[ RA2.S08.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">There are data that show the impact of the training program that are used to improve the program.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no evidence that the impact of the CPR training program is used to improve the program.<br />
1 A tool for evaluating the effectiveness of the resuscitation policy and procedure has been designed and tested.<br />
2 Results of the evaluation of resuscitation have been aggregated and displayed.<br />
3 An analysis of the results of resuscitation and mock drills have been documented and a plan for improvement has been implemented.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-xDsUWjkSsa7-val" name="entryfield" title="RA2.S08.L3" value="[ RA2.S08.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-tUh83KAOm9i-val" name="entryfield" title="RA2.S08.L3.Sc" value="[ RA2.S08.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 8 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S08" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S08" name="notes_RA1_S08" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section8')">Previous</button><button onclick="openTab('section10')">Next</button></div>
</div>
<!-- Section 10: Standard 9 -->

<div class="tab-content" id="section10"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #9: Staff performance management&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-Djrvld4CRi9-val" name="entryfield" title="RA2.S09.T" value="[ RA2.S09.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure describes the performance management process.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy and procedure for performance management.<br />
1 A current policy and procedure is in place that describes the performance management process.<br />
2 Each category of employee has a job-specific evaluation related to the assigned tasks described in the job description.<br />
3 Staff interviewed are aware of the process.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-XAfnr7c4ivt-val" name="entryfield" title="RA2.S09.L1" value="[ RA2.S09.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-W1sbC9sPJPH-val" name="entryfield" title="RA2.S09.L1.Sc" value="[ RA2.S09.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The performance management process is implemented according to the policy and procedure.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 Annual evaluations are not done and/or not consistently performed according to the policy and procedure.<br />
1 Personnel files contain individual annual performance evaluations conducted within the past 12 months.<br />
2 Performance goals/objectives are set with each employee with a plan to achieve these goals/objectives, which are linked to goals of the organization (for example, achieving targets).<br />
3 Feedback is provided to each staff member and progress toward the goals/objectives is documented.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-jeUe9Fvbz1s-val" name="entryfield" title="RA2.S09.L2" value="[ RA2.S09.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-zxv1A4zDQ78-val" name="entryfield" title="RA2.S09.L2.Sc" value="[ RA2.S09.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The effectiveness of the performance management process is evaluated.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 The effectiveness of the performance management system has not been evaluated within the past 15 months.<br />
1 A performance program evaluation tool is developed and tested, which measures whether the program functions according to the policy and procedure. At least the following questions are addressed:</p>

<p>&bull; Were evaluations done on time?<br />
&bull; Did everyone who was supposed to receive an evaluation get one?<br />
&bull; Were employee performance goals written and progress noted?<br />
2 An evaluation of the program has been conducted within the past 15 months.<br />
3 The results of the evaluation were analyzed, and actions taken to make improvements.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-iCkSBgdQTJg-val" name="entryfield" title="RA2.S09.L3" value="[ RA2.S09.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-H7vYefxOMjH-val" name="entryfield" title="RA2.S09.L3.Sc" value="[ RA2.S09.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 9 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S09" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S09" name="notes_RA1_S09" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section9')">Previous</button><button onclick="openTab('section11')">Next</button></div>
</div>
<!-- Section 11: Standard 10 -->

<div class="tab-content" id="section11"><!-- Standard header with overall score -->
<div class="standard-header">
<div class="standard-header-title">STANDARD #10: Staff health and safety program&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall Score:</div>

<div class="standard-header-score"><input id="jk0N8U8pqnL-IDZ2fmenPrQ-val" name="entryfield" title="RA2.S10.T" value="[ RA2.S10.T ]" /></div>
</div>
<!-- Div-based table -->

<div class="div-table"><!-- Table header -->
<div class="div-table-header">
<div class="div-table-header-col1">Levels of Effort</div>

<div class="div-table-header-col2">Performance Findings</div>

<div class="div-table-header-col3">Score</div>

<div class="div-table-header-col4">Overall Score</div>
</div>
<!-- Level 1 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 1:</h4>

<p class="level-description">A policy and procedure identifies how staff health is promoted and the management of work-related injuries and incidents. Staff injuries and health issues are attended to in a reactive manner as incidents occur.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no policy and procedure regarding promoting staff health and attending to staff illnesses and injuries.<br />
1 A policy and procedure describes procedures for promoting staff health and attending to staff illnesses and injuries.<br />
2 Department heads describe the process for completing incident reports when staff injuries and illnesses occur.<br />
3 Required follow up for incident reports includes staff referral for treatment for illnesses and injuries and the outcomes.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-V18tFxBOfNO-val" name="entryfield" title="RA2.S10.L1" value="[ RA2.S10.L1 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-MDDYaC0YcJw-val" name="entryfield" title="RA2.S10.L1.Sc" value="[ RA2.S10.L1.Sc ]" /></div>
</div>
<!-- Level 2 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 2:</h4>

<p class="level-description">The hospital has a proactive program to promote staff health and to identify and implement processes to reduce staff safety risks.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no proactive approach to staff health and safety.<br />
1 An employee occupational hazard risk assessment has been done.<br />
2 Plans are in place to promote staff health and reduce the potential risks to staff; plans include providing the following:<br />
a. Measures to promote staff health (for example, periodic health screening, promotion of a healthy lifestyle, emotional wellbeing)</p>

<p>b. Measures to prevent manual handling and needlestick injuries<br />
c. Education, evaluation, counseling, and follow-up for staff who are second victims of adverse or sentinel events<br />
d. Hepatitis B vaccine<br />
e. Annual TB tests for staff working in high-risk areas: OPD, emergency department, TB unit, laboratory, and HIV unit.<br />
3 Documentation in minutes or reports shows that the plans have been implemented.<br />
NOTE: Several of the occupational hazards are related to IPC and are included in the IPC policies and procedures. Other issues may include such things as back injuries or falls.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-OhlToqOA4iU-val" name="entryfield" title="RA2.S10.L2" value="[ RA2.S10.L2 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-zXmc06yhVX7-val" name="entryfield" title="RA2.S10.L2.Sc" value="[ RA2.S10.L2.Sc ]" /></div>
</div>
<!-- Level 3 -->

<div class="div-table-row"><!-- Column 1: Level description -->
<div class="div-table-cell div-table-cell-col1">
<h4 class="level-heading">Level 3:</h4>

<p class="level-description">The hospital collects and analyzes data on staff health, risks and injuries and can demonstrate increased safety and reduced health incidents.</p>
</div>
<!-- Column 2: Performance findings -->

<div class="div-table-cell div-table-cell-col2">
<p>0 There is no documentation of employee risks and injuries.<br />
1 The incident report data is aggregated and reported in graphs/charts.<br />
2 An action plan has been developed to reduce employee&rsquo;s risk of injuries.<br />
3 Data analysis shows progress in reducing staff injuries and illnesses.</p>
</div>
<!-- Column 3: Score -->

<div class="div-table-cell div-table-cell-col3"><input id="jk0N8U8pqnL-f62jXJeVftt-val" name="entryfield" title="RA2.S10.L3" value="[ RA2.S10.L3 ]" /></div>
<!-- Column 4: Overall Score -->

<div class="div-table-cell div-table-cell-col4"><input id="jk0N8U8pqnL-du1zian7RmU-val" name="entryfield" title="RA2.S10.L3.Sc" value="[ RA2.S10.L3.Sc ]" /></div>
</div>
</div>
<!-- Assessor Notes for Standard 10 -->

<div class="div-table-row">
<div class="div-table-cell" colspan="4" style="width: 100%; padding: 10px"><label for="notes_RA1_S10" style="font-weight: bold">Assessor Notes and Recommendations:</label><br />
<textarea id="notes_RA1_S10" name="notes_RA1_S10" rows="3" style="width: 100%; border: 1px solid #ccc; border-radius: 5px; resize: vertical;"></textarea></div>
</div>

<div class="navigation-buttons"><button onclick="openTab('section10')">Previous</button><button onclick="openTab('section12')">Next</button></div>
</div>
<script>
  // Function to open a tab
  function openTab(tabId) {
    var i;
    var x = document.getElementsByClassName("tab-content");
    var tabButtons = document.getElementsByClassName("tab-button");

    // Hide all tab contents and remove active class from all buttons
    for (i = 0; i < x.length; i++) {
      x[i].style.display = "none";
      tabButtons[i].classList.remove("active");
    }

    // Display the selected tab content and add the active class to the corresponding button
    document.getElementById(tabId).style.display = "block";
    document.getElementById(tabId).classList.add("active");

    // Add active class to the tab button corresponding to the selected tab content
    for (i = 0; i < tabButtons.length; i++) {
      if (tabButtons[i].getAttribute("onclick").includes(tabId)) {
        tabButtons[i].classList.add("active");
      }
    }
  }

  // Function to sync the Score input value to the Overall Score input
  function syncOverall(scoreId, overallId) {
    var scoreElem = document.getElementById(scoreId);
    var overallElem = document.getElementById(overallId);
    if (scoreElem && overallElem) {
      overallElem.value = scoreElem.value;
    }
  }
</script></div>
</div>
